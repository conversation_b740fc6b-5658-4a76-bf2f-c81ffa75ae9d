#!/usr/bin/env python
# -*- coding: utf-8 -*-
'''
@File	:food.py
@Time	:2025/05/14 17:23:53
<AUTHOR>
@Mail	:<EMAIL>
'''


import os
import pretty_errors
os.system('clear')
pretty_errors.configure(
    separator_character='*',
    filename_display=pretty_errors.FILENAME_EXTENDED,
    line_number_first=True,
    display_link=True,
    lines_before=5,
    lines_after=2,
    line_color=pretty_errors.RED+'> '+pretty_errors.BRIGHT_RED,
    filename_color=pretty_errors.YELLOW,
    header_color=pretty_errors.BRIGHT_GREEN,
    link_color=pretty_errors.BRIGHT_BLUE,
    code_color=pretty_errors.BRIGHT_RED,
    # code_color='  '+pretty_errors.default_config.line_color
    line_number_color=pretty_errors.BRIGHT_RED
)

import os
import asyncio
from typing import Optional, Dict, Any, List, Type
from abc import ABC, abstractmethod

# 使用 pydantic v2 而不是废弃的 langchain.pydantic_v1
from pydantic import BaseModel, Field
from langchain_core.tools import BaseTool, tool
from llm import sync_client, async_client
from prompt import FOOD_PROMPT, WEATHER_PROMPT, TRAFFIC_PROMPT
from location_helper import LocationHelper


# 基础输入模型
class BaseInput(BaseModel):
    """所有工具输入的基类"""
    pass


# 美食查询输入模型
class FoodsInput(BaseInput):
    location: Optional[str] = Field(description='Parameter defines the location of restaurants. e.g. 塘朗城广场')


class FoodsInputSchema(BaseModel):
    params: FoodsInput


# 天气查询输入模型
class WeatherInput(BaseInput):
    location: Optional[str] = Field(description='Parameter defines the location for weather query. e.g. 深圳')
    date: Optional[str] = Field(default=None, description='Parameter defines the date for weather query. e.g. 2025年5月12日')


class WeatherInputSchema(BaseModel):
    params: WeatherInput


# 交通查询输入模型
class TrafficInput(BaseInput):
    # origin: str, destination: str, mode: str = 'bus', city: str = '', city_d: str = ''
    origin: Optional[str] = Field(description='Parameter defines the origin location. e.g. "116.481028,39.989643"')
    destination: Optional[str] = Field(description='Parameter defines the destination location. e.g. "116.434446,39.90816"')
    mode: Optional[str] = Field(description='Parameter defines the mode of transportation. e.g. "bus"')
    city: Optional[str] = Field(description='Parameter defines the city of origin. e.g. "深圳"')
    city_d: Optional[str] = Field(description='Parameter defines the city of destination. e.g. "深圳"')


class TrafficInputSchema(BaseModel):
    params: TrafficInput


# 工具抽象基类
class ToolBase(ABC):
    """所有工具的抽象基类"""
    @abstractmethod
    async def execute(self, params: Any) -> str:
        """执行工具的方法"""
        pass


# 美食查询工具实现
class FoodsTool(ToolBase):
    async def execute(self, params: FoodsInput) -> str:
        """
        执行美食查询
        """
        params_dict = {
            'location': params.location,
        }

        completion = await async_client.chat.completions.create(
            model="qwen-plus-2025-01-12",
            messages=[{'role': 'system', 'content': FOOD_PROMPT},
                      {'role': 'user', 'content': f'{params_dict["location"]}附近有什么美食？'}],
            extra_body={
                "enable_search": True
            }
        )
        return completion.choices[0].message.content


# 天气查询工具实现
class WeatherTool(ToolBase):
    async def execute(self, params: WeatherInput) -> str:
        """
        执行天气查询
        """
        params_dict = {
            'location': params.location,
            'date': params.date,
        }

        completion = await async_client.chat.completions.create(
            model="qwen-plus-2025-01-12",
            messages=[{'role': 'system', 'content': WEATHER_PROMPT},
                      {'role': 'user', 'content': f'{params_dict["location"]}{params_dict["date"]}的天气怎么样？'}],
            extra_body={
                "enable_search": True
            }
        )
        return completion.choices[0].message.content


# 交通查询工具实现
class TrafficTool(ToolBase):
    async def execute(self, params: TrafficInput) -> str:
        """
        执行交通查询
        """
        # origin: str, destination: str, mode: str = 'bus', city: str = '', city_d: str = ''
        params_dict = {
            'origin': params.origin,
            'destination': params.destination,
            'mode': params.mode,
            'city': params.city,
            'city_d': params.city_d,
        }

        location_helper = LocationHelper()
        result = await location_helper.traffic_plan(**params_dict)
        return result

        # completion = await async_client.chat.completions.create(
        #     model="qwen-plus",
        #     messages=[{'role': 'system', 'content': TRAFFIC_PROMPT},
        #               {'role': 'user', 'content': f'从{params_dict["origin"]}到{params_dict["destination"]}怎么走？'}],
        #     extra_body={
        #         "enable_search": True
        #     }
        # )
        # return completion.choices[0].message.content


# 工具工厂类
class ToolFactory:
    """工具工厂，负责创建各种工具实例"""
    _tools: Dict[str, Type[ToolBase]] = {
        'foods': FoodsTool,
        'weather': WeatherTool,
        'traffic': TrafficTool,
    }
    
    @classmethod
    def get_tool(cls, tool_name: str) -> ToolBase:
        """获取工具实例"""
        if tool_name not in cls._tools:
            raise ValueError(f"Tool {tool_name} not found")
        return cls._tools[tool_name]()


# LangChain工具定义
# 美食查询工具
@tool(args_schema=FoodsInputSchema)
async def foods_finder(params: FoodsInput) -> str:
    '''
    查询指定位置附近的美食信息。
    
    Returns:
        str: 美食查询结果，包含餐厅地址、联系电话和美食类型。
    '''
    tool = ToolFactory.get_tool('foods')
    result = await tool.execute(params)
    print(result)  # 调试输出
    return result

# 天气查询工具
@tool(args_schema=WeatherInputSchema)
async def weather_finder(params: WeatherInput) -> str:
    '''
    查询指定位置的天气信息。
    
    Returns:
        str: 天气查询结果，包含当前天气状况、温度、湿度、风力等信息，以及未来24小时的天气预报。
    '''
    tool = ToolFactory.get_tool('weather')
    result = await tool.execute(params)
    print(result)  # 调试输出
    return result

# 交通查询工具
@tool(args_schema=TrafficInputSchema)
async def traffic_finder(params: TrafficInput) -> str:
    '''
    查询从出发地到目的地的最佳出行路线。
    
    Returns:
        str: 交通查询结果，包含预计时间、距离、交通方式等信息。
    '''
    tool = ToolFactory.get_tool('traffic')
    result = await tool.execute(params)
    print(result)  # 调试输出
    return result


async def run_tools():
    # 准备输入参数
    food_input = {"params": FoodsInput(location='塘朗城广场')}
    weather_input = {"params": WeatherInput(location='深圳', date='2025年5月12日')}
    traffic_input = {"params": TrafficInput(origin='深圳北站', destination='深圳湾公园')}
    
    # 使用 gather 并行执行所有查询
    print("===== 开始并行查询 =====\n")
    tasks = [
        foods_finder.ainvoke(food_input),
        weather_finder.ainvoke(weather_input),
        traffic_finder.ainvoke(traffic_input)
    ]
    
    # 等待所有任务完成
    results = await asyncio.gather(*tasks)
    
    # 打印结果
    print("\n===== 美食查询结果 =====")
    print(results[0])
    
    print("\n===== 天气查询结果 =====")
    print(results[1])
    
    print("\n===== 交通查询结果 =====")
    print(results[2])


if __name__ == "__main__":
    asyncio.run(run_tools())
