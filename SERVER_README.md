# 约会规划服务器

基于 FastAPI 和 SSE（Server-Sent Events）的约会规划服务，提供实时流式数据传输。

## 功能特性

- 🚀 **FastAPI 框架**：高性能异步 Web 框架
- 📡 **SSE 流式传输**：实时数据流，用户体验更佳
- 🎯 **约会规划**：智能约会建议和详细规划
- 🌐 **现代前端**：响应式设计，支持移动端
- 📍 **位置服务**：基于地理位置的智能推荐

## API 端点

### 1. 主页
- **GET** `/` - 返回前端页面

### 2. 解释约会期望
- **POST** `/explain-request` - 解释用户约会期望
- 返回：JSON 格式的解释结果

### 3. 思考规划（SSE）
- **POST** `/think-plan` - 用户需求理解和任务拆分
- 返回：SSE 流式数据

### 4. 查询目的地信息
- **POST** `/search-location-info` - 查询目的地周边信息
- 返回：JSON 格式的 POI 信息

### 5. 获取详细计划（SSE）
- **POST** `/get-detailed-plan` - 生成详细约会计划
- 返回：SSE 流式数据

## 安装和运行

### 1. 安装依赖
```bash
pip install -r requirements.txt
```

### 2. 启动服务器
```bash
python run_server.py
```

或者直接运行：
```bash
python server.py
```

### 3. 访问服务
- 前端页面：http://localhost:8000
- API 文档：http://localhost:8000/docs
- 交互式文档：http://localhost:8000/redoc

## 使用说明

1. **打开前端页面**：访问 http://localhost:8000
2. **填写约会参数**：在表单中输入约会相关信息
3. **选择功能**：
   - 点击"解释约会期望"获取期望解释
   - 点击"思考规划"查看实时思考过程
   - 点击"查询目的地信息"获取周边信息
   - 点击"获取详细计划"生成完整约会计划

## 技术架构

### 后端
- **FastAPI**：Web 框架
- **SSE-Starlette**：Server-Sent Events 支持
- **Pydantic**：数据验证
- **Uvicorn**：ASGI 服务器

### 前端
- **HTML5**：现代语义化标记
- **CSS3**：响应式设计和动画
- **JavaScript ES6+**：EventSource API 处理 SSE
- **Jinja2**：模板引擎

### 数据流
1. 用户在前端填写表单
2. JavaScript 收集数据并发送 POST 请求
3. FastAPI 处理请求并调用相应的业务逻辑
4. 对于流式端点，使用 SSE 实时返回数据
5. 前端通过 EventSource 接收并显示实时数据

## 文件结构

```
.
├── server.py              # FastAPI 服务器主文件
├── run_server.py          # 启动脚本
├── templates/
│   └── index.html         # 前端页面模板
├── static/
│   ├── style.css          # 样式文件
│   └── script.js          # JavaScript 脚本
├── requirements.txt       # Python 依赖
└── SERVER_README.md       # 本文档
```

## 开发说明

### SSE 实现
服务器使用 `EventSourceResponse` 实现 SSE：
```python
async def generate():
    async for content in some_async_generator():
        yield {
            "event": "message",
            "data": json.dumps({"content": content})
        }

return EventSourceResponse(generate())
```

前端使用 `fetch` API 处理 SSE 流：
```javascript
const response = await fetch('/endpoint', {
    method: 'POST',
    headers: {'Content-Type': 'application/json'},
    body: JSON.stringify(data)
});

const reader = response.body.getReader();
// 处理流式数据...
```

### 扩展功能
要添加新的 API 端点：
1. 在 `server.py` 中定义新的路由
2. 在前端添加相应的按钮和处理函数
3. 更新样式和用户界面

## 注意事项

- 确保所有依赖的模块（如 `main.py`, `llm.py` 等）都在同一目录下
- SSE 连接在某些代理或防火墙环境下可能受限
- 生产环境建议使用 Nginx 等反向代理
- 建议配置适当的 CORS 策略用于跨域访问
