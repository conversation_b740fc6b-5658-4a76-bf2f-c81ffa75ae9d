#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
启动脚本 - 约会规划服务器
"""

import uvicorn
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

if __name__ == "__main__":
    print("启动约会规划服务器...")
    print("访问地址: http://localhost:8000")
    print("API 文档: http://localhost:8000/docs")

    uvicorn.run(
        "server:app",
        host="0.0.0.0",
        port=8000,
        reload=True,  # 开发模式下自动重载
        log_level="info"
    )
