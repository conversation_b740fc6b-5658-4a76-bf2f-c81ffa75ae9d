# TODO 从获取到的电影列表里找到每一部电影的cinema_id,

# 通过cinema_id和接口https://apis.netstart.cn/maoyan/movie/intro?movieId=1331230获取每一部电影的图片，简介，订购网址，并合并到上一个json数据。

# 最后把这个合并后的json喂给llm，选三部与用户喜好风格相似的电影。生成带电影信息的json,电影信息一个字段，订购链接单独一个字段，电影图片一个字段，电影简介一个字段

#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
@File    :cinema_service.py
@Time    :2025/05/13
<AUTHOR>
@Mail    :<EMAIL>
"""

import sys
from pathlib import Path

PROJECT_ROOT = Path(__file__).absolute().parents[1].absolute()
sys.path.insert(0, str(PROJECT_ROOT))
from find_similar_cinema import CinemaSimilarityFinder
from logger import LOGGER
from const import TODAY
from typing import List, Dict, Any, Optional, Tuple
import datetime
import aiohttp
import asyncio
import json
import os



class CinemaService:
    """
    电影院服务类，提供获取城市、影院、电影等相关信息的功能
    """

    _instance = None

    def __new__(cls, *args, **kwargs):
        if cls._instance is None:
            cls._instance = super(CinemaService, cls).__new__(cls)
            cls._instance._initialized = False
        return cls._instance

    def __init__(self):
        if self._initialized:
            return

        self.base_url = "https://apis.netstart.cn/maoyan"
        self.headers = {
            "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Referer": "https://apis.netstart.cn/maoyan/",
            "Accept": "application/json, text/plain, */*",
            "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
            "Origin": "https://apis.netstart.cn",
        }
        self.similar_finder = CinemaSimilarityFinder()
        self._initialized = True

    async def get_city_by_location(self, lat: float, lng: float) -> dict:
        """
        根据经纬度获取当前城市信息

        Args:
            lat: 地理定位纬度
            lng: 地理定位经度

        Returns:
            dict: 城市信息，包含城市ID、名称等

        示例:
            >>> await get_city_by_location(23.4, 113.3)
            {'ci': 20, 'nm': '广州', ...}
        """
        # 构建请求 URL
        url = f"{self.base_url}/city/latlng"

        # 设置请求参数
        params = {"lat": lat, "lng": lng}

        try:
            # 使用异步会话发送 GET 请求
            async with aiohttp.ClientSession() as session:
                async with session.get(
                    url, params=params,
                    headers=self.headers,
                    ssl=False
                ) as response:
                    # 检查请求是否成功
                    response.raise_for_status()

                    # 解析 JSON 响应
                    data = await response.json()

                    # 检查响应是否包含数据
                    if data and "data" in data:
                        city_data = data["data"]
                        LOGGER.info(f"获取城市信息成功: {city_data['city']}")
                        return city_data
                    else:
                        LOGGER.error(f"获取城市信息失败: {data.get('msg', '未知错误')}")
                        return {}
        except Exception as e:
            LOGGER.error(f"获取城市信息异常: {str(e)}")
            return {}

    async def get_cinema_filters(self, city_id: str) -> dict:
        """
        获取影院筛选条件

        Args:
            city_id: 城市ID

        Returns:
            dict: 影院筛选条件，包含行政区、品牌等信息
        """
        # 构建请求 URL
        url = f"{self.base_url}/index/filterCinemas"

        # 设置请求参数
        params = {"ci": city_id}

        try:
            # 使用异步会话发送 GET 请求
            async with aiohttp.ClientSession() as session:
                async with session.get(
                    url, params=params,
                    headers=self.headers,
                    ssl=False
                ) as response:
                    # 检查请求是否成功
                    response.raise_for_status()

                    # 解析 JSON 响应
                    data = await response.json()

                    # 检查响应是否包含数据
                    if data and "data" in data:
                        LOGGER.info(f"获取影院筛选条件成功")
                        return data["data"]
                    else:
                        LOGGER.error(
                            f"获取影院筛选条件失败: {data.get('msg', '未知错误')}"
                        )
                        return {}
        except Exception as e:
            LOGGER.error(f"获取影院筛选条件异常: {str(e)}")
            return {}

    async def get_nearby_cinemas(
        self,
        city_id: str,
        to_location: str,
        lat: float,
        lng: float,
        district_id: str = "-1",
        limit: int = 20,
        watch_movie_date: str = TODAY,
    ) -> list:
        """
        获取附近影院列表

        Args:
            city_id: 城市ID
            lat: 纬度
            lng: 经度
            to_location: 目标位置(约会地点)，如"cocopark"
            district_id: 行政区ID，默认为-1（全部）
            limit: 返回结果数量限制

        Returns:
            list: 附近影院列表
        """
        # API 基础 URL
        base_url = "https://apis.netstart.cn/maoyan"

        # 构建请求 URL
        url = f"{base_url}/index/moreCinemas"

        # 获取当前日期，格式为 YYYY-MM-DD

        # today = datetime.datetime.now().strftime("%Y-%m-%d")

        # 设置请求参数
        params = {
            "day": TODAY,
            "offset": 0,
            "limit": limit,
            "districtId": district_id,
            "lineId": "-1",
            "hallType": "-1",
            "brandId": "-1",
            "serviceId": "-1",
            "areaId": "-1",
            "stationId": "-1",
            "item": "",
            "updateShowDay": "true",
            "reqId": str(int(datetime.datetime.now().timestamp() * 1000)),
            "cityId": city_id,
            "lat": lat,
            "lng": lng,
        }

        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(
                    url, params=params,
                    headers=self.headers,
                    ssl=False
                ) as response:
                    # 检查请求是否成功
                    response.raise_for_status()

                    # 解析 JSON 响应
                    data = await response.json()

            # 打印完整响应以便调试
            LOGGER.info(f"附近影院响应: {data}")

            # 使用向量相似度搜索与to_location最相近的影院名字
            if data and isinstance(data, list) and len(data) > 0:
                # 需要先处理原始数据，确保字段名称的一致性
                processed_cinemas = []
                for cinema in data:
                    # 创建一个新的影院对象，确保有id字段
                    processed_cinema = {
                        "id": cinema.get("cinemaId"),  # 使用cinemaId作为id
                        "title": cinema.get("title"),
                        "location": cinema.get("location"),
                    }
                    processed_cinemas.append(processed_cinema)

                most_similar = await self.similar_finder.find_most_similar(
                    cinemas=processed_cinemas,
                    query=to_location,
                    fields=["id", "title", "location"],
                )

                LOGGER.info(f'most_similar:{most_similar}')
                return most_similar
            else:
                LOGGER.error("未获取到影院数据")
                return None

        except aiohttp.ClientError as e:
            LOGGER.error(f"aiohttp请求错误: {e}")
            return None
        except json.JSONDecodeError:
            LOGGER.error("解析响应数据失败")
            return []

    async def get_cinema_shows(
        self,
        cinema_id: str,
        city_id: str,
        channel_id: str = "4",
        watch_movie_date: str = TODAY,
    ) -> dict:
        """
        获取影院正在上映电影列表

        Args:
            cinema_id: 影院ID
            city_id: 城市ID
            channel_id: 通道ID，默认为4
            watch_movie_date: 观影日期，默认为今天

        Returns:
            dict: 影院当前上映影片信息，包含演出时间、票价等
        """
        LOGGER.info(f"获取影院 {cinema_id} 的电影列表...")

        # 构建请求 URL
        url = f"{self.base_url}/cinema/shows"

        # 设置请求参数
        params = {
            "cinemaId": cinema_id,
            "ci": city_id,
            "channelId": channel_id,  # 默认为电影
        }

        max_retries = 3
        retry_count = 0

        while retry_count < max_retries:
            try:
                async with aiohttp.ClientSession() as session:
                    async with session.get(
                        url, params=params,
                        headers=self.headers,
                        verify_ssl=False
                    ) as response:
                        response.raise_for_status()
                        data = await response.json()

                        if data.get("data"):
                            LOGGER.info(f"获取影院 {cinema_id} 的电影列表成功")
                            # 过滤电影数据，只保留需要的字段
                            filtered_data = self.filter_movie_data(
                                data, watch_movie_date=watch_movie_date
                            )
                            LOGGER.info(filtered_data["data"])
                            return filtered_data["data"]
                        else:
                            LOGGER.error(
                                f"获取影院电影列表失败: {data.get('msg', '未知错误')}"
                            )
                            return {}
            except Exception as e:
                retry_count += 1
                if retry_count < max_retries:
                    LOGGER.warning(
                        f"获取影院电影列表失败，正在重试({retry_count}/{max_retries}): {str(e)}"
                    )
                    await asyncio.sleep(1)  # 等待1秒后重试
                else:
                    LOGGER.error(f"获取影院电影列表失败，已达到最大重试次数: {str(e)}")
                    return {}

    def filter_movie_data(self, data: dict, watch_movie_date: str = TODAY) -> dict:
        """
        过滤影院电影数据，只保留指定字段

        Args:
            data: 原始API返回的完整数据
            watch_movie_date: 要过滤的观影日期，格式为"yyyy-mm-dd"，如果提供则只返回该日期的放映场次
        Returns:
            dict: 只包含指定字段的过滤后数据
        """
        if not data or not data.get("data"):
            return data

        result = {
            "code": data.get("code"),
            "data": {
                "cinemaId": data["data"].get("cinemaId"),
                "cinemaName": data["data"].get("cinemaName"),
                "movies": [],
            },
        }

        # 过滤电影数据
        if data["data"].get("movies"):
            for movie in data["data"]["movies"]:
                # 跳过空对象
                if not movie:
                    continue

                filtered_movie = {
                    "desc": movie.get("desc"),
                    "dur": movie.get("dur"),
                    "id": movie.get("id"),
                    "nm": movie.get("nm"),
                    "sc": movie.get("sc"),
                    "shows": [],  # 初始化为空列表，避免直接引用原始数据
                }

                # 过滤场次数据
                if movie.get("shows"):
                    for show in movie["shows"]:
                        filtered_show = {"hasShow": show.get(
                            "hasShow"), "plist": []}

                        # 过滤价格列表数据
                        if show.get("plist"):
                            for price in show["plist"]:
                                # 如果指定了日期，只保留该日期的场次
                                if (
                                    watch_movie_date
                                    and price.get("dt") != watch_movie_date
                                ):
                                    continue

                                filtered_price = {
                                    "discountSellPrice": price.get("discountSellPrice"),
                                    "dt": price.get("dt"),
                                    "lang": price.get("lang"),
                                    "tm": price.get("tm"),
                                    "tp": price.get("tp"),
                                }
                                filtered_show["plist"].append(filtered_price)

                        # 只添加有场次的show
                        if filtered_show["plist"]:
                            filtered_movie["shows"].append(filtered_show)

                # 只添加有shows的电影
                if filtered_movie["shows"]:
                    result["data"]["movies"].append(filtered_movie)

        return result

    def extract_movie_info(self, cinema_shows_data: dict) -> list:
        """
        从影院上映数据中提取电影信息

        Args:
            cinema_shows_data: get_cinema_shows函数返回的原始数据

        Returns:
            list: 电影信息列表，包含影片名称、演出时间、票价等
        """
        result = []

        # 检查影片列表
        if not cinema_shows_data or not cinema_shows_data.get("movies"):
            return result

        # 调试信息 - 打印第一部电影的shows字段结构
        LOGGER.debug("查看数据结构")
        if cinema_shows_data.get("movies") and len(cinema_shows_data["movies"]) > 0:
            first_movie = cinema_shows_data["movies"][0]
            if first_movie.get("shows") and len(first_movie["shows"]) > 0:
                first_show = first_movie["shows"][0]
                if first_show.get("plist") and len(first_show["plist"]) > 0:
                    # 打印完整的plist示例以便调试
                    LOGGER.debug("场次数据结构示例")
                    LOGGER.debug(
                        json.dumps(first_show["plist"][0],
                                   ensure_ascii=False, indent=2)
                    )

        # 遍历每部影片
        for movie in cinema_shows_data["movies"]:
            movie_info = {
                "id": movie.get("id"),
                "name": movie.get("nm"),
                "duration": movie.get("dur"),  # 片长
                "score": movie.get("sc"),  # 评分
                "shows": [],  # 场次信息
            }

            # 遍历该影片的所有场次
            if movie.get("shows"):
                for show in movie["shows"]:
                    # 如果show有plist字段并且不为空
                    if show.get("plist") and len(show["plist"]) > 0:
                        for price_item in show["plist"]:
                            show_info = {
                                "hall": price_item.get("th"),  # 放映厅
                                "time": price_item.get("tm"),  # 放映时间
                                # 售价
                                "price": price_item.get("discountSellPrice"),
                                "lang": price_item.get("lang"),  # 语言
                                "dim": price_item.get("tp"),  # 维度（3D/2D）
                            }
                            movie_info["shows"].append(show_info)

            result.append(movie_info)

        return result

    async def get_movie_detail_info(self, movie_id: str) -> dict:
        """获取某部电影详细信息"""
        url = f'{self.base_url}/movie/intro'
        try:
            LOGGER.info(f"调用API获取电影详情: {url}?movieId={movie_id}")
            async with aiohttp.ClientSession() as session:
                async with session.get(url, params={"movieId": movie_id}) as response:
                    if response.status == 200:
                        response_json = await response.json()
                        LOGGER.info(
                            f"API响应状态码: 200, 响应数据结构: {list(response_json.keys()) if response_json else None}")
                        share_info = response_json.get('data', {}).get(
                            'movie', {}).get('shareInfo')
                        if share_info:
                            introduce = share_info.get('content')
                            img = share_info.get('img')
                            buy_link = share_info.get('url')
                            LOGGER.info(f"成功解析电影 {movie_id} 的详细信息")
                            return {
                                # 'movie_id': movie_id,
                                'introduce': introduce,
                                'img': img,
                                'buy_link': buy_link
                            }
                        else:
                            LOGGER.warning(f"未找到电影 {movie_id} 的shareInfo数据")

                    else:
                        LOGGER.error(
                            f"获取电影 {movie_id} 详情API响应错误，状态码: {response.status}")
                        return None
        except Exception as e:
            LOGGER.error(f"获取电影 {movie_id} 详细信息失败: {str(e)}")
            return None

    def save_movie_data(
        self, data: dict, filename: str = "filtered_movie_data.json"
    ) -> str:
        """保存电影数据到JSON文件"""
        try:
            filepath = os.path.join(
                os.path.dirname(os.path.abspath(__file__)), filename
            )
            with open(filepath, "w", encoding="utf-8") as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            LOGGER.info(f"已将电影数据保存到: {filepath}")
            return filepath
        except Exception as e:
            LOGGER.error(f"保存电影数据失败: {str(e)}")
            return ""


# 工厂类
class CinemaServiceFactory:
    @staticmethod
    def create_cinema_service() -> CinemaService:
        """创建电影院服务实例"""
        return CinemaService()


async def search_movies(to_location: str, to_location_lat: float, to_location_lng: float, watch_movie_date: str):

    # 使用工厂模式创建服务实例
    service = CinemaServiceFactory.create_cinema_service()

    # 1. 获取深圳市的信息
    city_info = await service.get_city_by_location(
        to_location_lat, to_location_lng
    )  # 深圳的经纬度

    if city_info:
        city_id = city_info.get("id")
        LOGGER.info(f"找到城市: {city_info.get('city')} (ID: {city_id})")

        # 2. 获取最近的影院，找到与cocopark最相似的影院
        cinemas = await service.get_nearby_cinemas(
            city_id=str(city_id),
            to_location=to_location,
            lat=to_location_lat,
            lng=to_location_lng,
            watch_movie_date=watch_movie_date
        )

        if cinemas:
            # find_most_similar 返回的是单个影院字典，而不是列表
            most_similar_cinema = cinemas  # 最相似的影院
            cinema_id = most_similar_cinema.get("id") or most_similar_cinema.get(
                "cinemaId"
            )
            LOGGER.info(f"找到最相似的影院: {most_similar_cinema.get('title')}")

            # 3. 获取影院的电影列表
            cinema_shows_data = await service.get_cinema_shows(
                str(cinema_id), str(city_id), watch_movie_date=watch_movie_date
            )

            LOGGER.info(f"影院电影列表: {cinema_shows_data}")

            # 为每部电影添加详细信息（介绍、图片和购买链接）
            if cinema_shows_data and 'movies' in cinema_shows_data:
                for movie in cinema_shows_data['movies']:
                    movie_id = str(movie.get('id'))
                    LOGGER.info(
                        f"获取电影 {movie.get('nm')}(ID: {movie_id}) 的详细信息")
                    movie_detail_info = await service.get_movie_detail_info(movie_id)
                    LOGGER.info(f"电影 {movie_id} 详细信息获取结果: {movie_detail_info}")

                    if movie_detail_info:
                        # 将详细信息添加到电影字典中
                        movie['introduce'] = movie_detail_info.get('introduce')
                        movie['img'] = movie_detail_info.get('img')
                        movie['buy_link'] = movie_detail_info.get('buy_link')
                        LOGGER.info(f"已添加电影 {movie_id} 的详细信息")

            LOGGER.info(f"添加详细信息后的电影列表: {cinema_shows_data}")
            return cinema_shows_data

            # if cinema_shows_data:
            #     # 4. 可选：保存数据到文件
            #     service.save_movie_data(cinema_shows_data)

            #     # 5. 提取并显示电影信息
            #     movies = service.extract_movie_info(cinema_shows_data)
            #     LOGGER.info(f"共找到 {len(movies)} 部正在上映的电影")

            #     # 显示每部电影的信息及场次
            #     for i, movie in enumerate(movies, 1):
            #         LOGGER.info(
            #             f"\n{i}. {movie['name']} (评分: {movie.get('score', '暂无')}, 时长: {movie.get('duration', '未知')}分钟)"
            #         )

            #         # 只展示前3个场次
            #         if movie["shows"]:
            #             for j, show in enumerate(movie["shows"][:3], 1):
            #                 LOGGER.info(
            #                     f"   {j}) {show.get('time', '00:00')} [{show.get('hall', '普通厅')}] {show.get('lang', '')} {show.get('dim', '')} • 售价: {show.get('price', '未知')}元"
            #                 )
            #             if len(movie["shows"]) > 3:
            #                 LOGGER.info(f"   ... 共{len(movie['shows'])}场放映")
            #         else:
            #             LOGGER.info("   暂无场次信息")


if __name__ == "__main__":
    asyncio.run(search_movies(to_location='cocopark', to_location_lat=22.520922,
                to_location_lng=114.055198, watch_movie_date='2025-05-26'))
