#!/usr/bin/env python3
"""
智能限流服务器并发能力分析
"""

import math

class ConcurrentCapacityAnalyzer:
    def __init__(self):
        # 基础限流配置（每分钟）
        self.base_limits = {
            "explain-request": 100,
            "think-plan": 50, 
            "search-location-info": 80,
            "get-detailed-plan": 30
        }
        
        # 负载倍数
        self.load_multipliers = {
            "low": 3.0,    # CPU<50%, 内存<70%
            "medium": 2.0, # CPU<70%, 内存<80%
            "high": 0.5    # 高负载保护模式
        }
        
        # 可信用户额外倍数
        self.trusted_user_multiplier = 1.5

    def calculate_limits_by_load(self, load_level: str):
        """计算不同负载下的限流值"""
        multiplier = self.load_multipliers[load_level]
        limits = {}
        
        for endpoint, base_limit in self.base_limits.items():
            # 普通用户限制
            normal_limit = int(base_limit * multiplier)
            # 可信用户限制
            trusted_limit = int(normal_limit * self.trusted_user_multiplier)
            
            limits[endpoint] = {
                "normal": normal_limit,
                "trusted": trusted_limit
            }
        
        return limits

    def estimate_concurrent_users(self, load_level: str, endpoint: str, user_behavior: str):
        """估算并发用户数"""
        limits = self.calculate_limits_by_load(load_level)
        
        if user_behavior == "normal":
            requests_per_minute = limits[endpoint]["normal"]
        else:  # trusted
            requests_per_minute = limits[endpoint]["trusted"]
        
        # 不同使用模式的并发估算
        scenarios = {
            # 用户每30秒发一次请求
            "casual_user": {
                "requests_per_user_per_minute": 2,
                "concurrent_users": requests_per_minute // 2
            },
            
            # 用户每10秒发一次请求  
            "active_user": {
                "requests_per_user_per_minute": 6,
                "concurrent_users": requests_per_minute // 6
            },
            
            # 用户每5秒发一次请求
            "power_user": {
                "requests_per_user_per_minute": 12,
                "concurrent_users": requests_per_minute // 12
            },
            
            # 性能测试场景（连续请求）
            "stress_test": {
                "requests_per_user_per_minute": 60,
                "concurrent_users": requests_per_minute // 60
            }
        }
        
        return scenarios

    def generate_capacity_report(self):
        """生成完整的并发能力报告"""
        print("🧠 智能限流服务器 - 并发能力分析报告")
        print("=" * 80)
        
        for load_level in ["low", "medium", "high"]:
            print(f"\n📊 {load_level.upper()} 负载场景")
            print("-" * 50)
            
            limits = self.calculate_limits_by_load(load_level)
            
            for endpoint in self.base_limits.keys():
                print(f"\n🎯 {endpoint.upper()} 端点:")
                print(f"   普通用户限制: {limits[endpoint]['normal']} 请求/分钟")
                print(f"   可信用户限制: {limits[endpoint]['trusted']} 请求/分钟")
                
                # 分析不同用户行为下的并发能力
                for user_type in ["normal", "trusted"]:
                    scenarios = self.estimate_concurrent_users(load_level, endpoint, user_type)
                    
                    print(f"\n   {user_type.upper()} 用户并发能力:")
                    for scenario_name, data in scenarios.items():
                        if data["concurrent_users"] > 0:
                            print(f"     • {scenario_name}: {data['concurrent_users']} 并发用户")

    def calculate_total_system_capacity(self):
        """计算系统总体并发能力"""
        print(f"\n🚀 系统总体并发能力评估")
        print("=" * 80)
        
        # 假设用户请求分布
        endpoint_usage_ratio = {
            "explain-request": 0.4,      # 40% 用户使用
            "think-plan": 0.3,           # 30% 用户使用  
            "search-location-info": 0.2, # 20% 用户使用
            "get-detailed-plan": 0.1     # 10% 用户使用
        }
        
        for load_level in ["low", "medium", "high"]:
            print(f"\n📈 {load_level.upper()} 负载下的系统容量:")
            
            limits = self.calculate_limits_by_load(load_level)
            
            # 计算瓶颈端点（最限制性的端点）
            bottleneck_capacity = float('inf')
            bottleneck_endpoint = ""
            
            for endpoint, ratio in endpoint_usage_ratio.items():
                if ratio > 0:
                    # 假设50%普通用户，50%可信用户
                    avg_limit = (limits[endpoint]['normal'] + limits[endpoint]['trusted']) / 2
                    # 假设活跃用户模式（每10秒一次请求）
                    capacity = (avg_limit // 6) / ratio
                    
                    if capacity < bottleneck_capacity:
                        bottleneck_capacity = capacity
                        bottleneck_endpoint = endpoint
            
            print(f"   瓶颈端点: {bottleneck_endpoint}")
            print(f"   理论最大并发用户: {int(bottleneck_capacity)} 用户")
            print(f"   推荐并发用户: {int(bottleneck_capacity * 0.8)} 用户 (80%安全边际)")

    def real_world_scenarios(self):
        """真实世界使用场景分析"""
        print(f"\n🌍 真实世界使用场景")
        print("=" * 80)
        
        scenarios = {
            "个人博客/小网站": {
                "concurrent_users": "10-50",
                "load_level": "low",
                "description": "用户偶尔使用，服务器负载很低"
            },
            
            "中小企业应用": {
                "concurrent_users": "50-200", 
                "load_level": "low-medium",
                "description": "员工日常使用，负载适中"
            },
            
            "热门应用/网站": {
                "concurrent_users": "200-1000",
                "load_level": "medium-high", 
                "description": "大量用户访问，需要智能调节"
            },
            
            "病毒式传播": {
                "concurrent_users": "1000+",
                "load_level": "high",
                "description": "突发大量流量，保护模式启动"
            }
        }
        
        for scenario, info in scenarios.items():
            print(f"\n📱 {scenario}:")
            print(f"   并发用户: {info['concurrent_users']}")
            print(f"   负载水平: {info['load_level']}")
            print(f"   说明: {info['description']}")

def main():
    analyzer = ConcurrentCapacityAnalyzer()
    
    # 生成详细报告
    analyzer.generate_capacity_report()
    analyzer.calculate_total_system_capacity()
    analyzer.real_world_scenarios()
    
    print(f"\n💡 关键优势:")
    print("   • 🎯 动态调节：根据服务器负载自动调整限制")
    print("   • 🤝 用户友好：识别正常用户，给予更多配额")
    print("   • 🛡️  自我保护：高负载时自动降级，防止崩溃")
    print("   • 📊 透明监控：实时显示负载和限制状态")

if __name__ == "__main__":
    main()
