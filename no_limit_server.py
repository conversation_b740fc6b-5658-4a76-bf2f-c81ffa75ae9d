#!/usr/bin/env python3
"""
无限流版本的约会规划服务器
适合开发、测试和高性能场景
"""

import asyncio
import json
import time
from typing import Dict, Any
import uvicorn
from fastapi import FastAPI, Request
from fastapi.responses import HTMLResponse
from fastapi.staticfiles import StaticFiles
from sse_starlette.sse import EventSourceResponse

# 导入业务逻辑
from dating_agent import (
    explain_request, think_plan, 
    search_purpose_location_info, get_detailed_plan,
    RequestParams
)

# 导入优化器（但不使用限流功能）
from performance_optimizer import PerformanceOptimizer

app = FastAPI(title="约会规划服务 - 无限流版", version="2.0.0")

# 初始化优化器（只使用缓存和并发控制，不使用限流）
optimizer = PerformanceOptimizer()

# 性能监控装饰器（简化版，无限流）
def monitor_performance(func):
    async def wrapper(*args, **kwargs):
        start_time = time.time()
        try:
            result = await func(*args, **kwargs)
            duration = time.time() - start_time
            print(f"✅ {func.__name__} 执行成功，耗时: {duration:.2f}s")
            return result
        except Exception as e:
            duration = time.time() - start_time
            print(f"❌ {func.__name__} 执行失败，耗时: {duration:.2f}s，错误: {str(e)}")
            raise
    return wrapper

# 静态文件和模板
app.mount("/static", StaticFiles(directory="static"), name="static")

@app.get("/", response_class=HTMLResponse)
async def read_root():
    with open("templates/index.html", "r", encoding="utf-8") as f:
        return HTMLResponse(content=f.read())

@app.get("/health")
async def health_check():
    return {"status": "healthy", "timestamp": time.time(), "rate_limiting": "disabled"}

@app.get("/metrics")
async def get_metrics():
    return {
        "performance": {
            "cache_size": len(optimizer.cache),
            "concurrent_limit": optimizer.max_concurrent,
            "rate_limiting": "disabled"
        },
        "timestamp": time.time()
    }

@app.post("/explain-request")
@monitor_performance
async def explain_request_endpoint(params: RequestParams, request: Request):
    """约会期望解释 - 无限流版"""
    async def generate():
        try:
            yield {
                "event": "info",
                "data": json.dumps({"content": "正在分析约会期望...\n", "type": "explain_request"})
            }

            await asyncio.sleep(0.1)
            params_dict = params.model_dump()

            # 使用缓存
            cache_key = f"explain:{hash(str(params_dict))}"
            cached_result = await optimizer._get_cache(cache_key)

            if cached_result:
                result = cached_result
            else:
                result = explain_request(params_dict)
                await optimizer._set_cache(cache_key, result, 1800)  # 30分钟缓存

            yield {
                "event": "message",
                "data": json.dumps({"content": result, "type": "explain_request"})
            }

            yield {
                "event": "success",
                "data": json.dumps({"message": "约会期望解释完成"})
            }
        except Exception as e:
            yield {
                "event": "error",
                "data": json.dumps({"error": str(e)})
            }

    return EventSourceResponse(generate())

@app.post("/think-plan")
@monitor_performance
async def think_plan_endpoint(params: RequestParams, request: Request):
    """思考规划 - 无限流版"""
    async def generate():
        try:
            params_dict = params.model_dump()
            async with optimizer.concurrent_limit():
                async for content in think_plan(params_dict):
                    yield {
                        "event": "message",
                        "data": json.dumps({"content": content, "type": "think_plan"})
                    }
            yield {
                "event": "success",
                "data": json.dumps({"message": "思考规划完成"})
            }
        except Exception as e:
            yield {
                "event": "error",
                "data": json.dumps({"error": str(e)})
            }

    return EventSourceResponse(generate())

@app.post("/search-location-info")
@monitor_performance
async def search_location_info_endpoint(params: RequestParams, request: Request):
    """查询目的地信息 - 无限流版"""
    async def generate():
        try:
            yield {
                "event": "info",
                "data": json.dumps({"content": "开始查询目的地信息...\n", "type": "search_location_info"})
            }

            params_dict = params.model_dump()

            # 使用缓存
            location_key = f"{params_dict['dating_params']['to_location_province']}-{params_dict['dating_params']['to_location_city']}-{params_dict['dating_params']['to_location_name']}"
            cache_key = f"location:{hash(location_key)}"

            cached_result = await optimizer._get_cache(cache_key)

            if cached_result:
                result = cached_result
            else:
                async with optimizer.concurrent_limit():
                    result = await search_purpose_location_info(params_dict)
                    await optimizer._set_cache(cache_key, result, 3600)  # 1小时缓存

            final_result = {"success": True, "data": result}

            yield {
                "event": "message",
                "data": json.dumps({"content": json.dumps(final_result, ensure_ascii=False, indent=2), "type": "search_location_info"})
            }

            yield {
                "event": "success",
                "data": json.dumps({"message": "目的地信息查询完成"})
            }

        except Exception as e:
            yield {
                "event": "error",
                "data": json.dumps({"error": str(e)})
            }

    return EventSourceResponse(generate())

@app.post("/get-detailed-plan")
@monitor_performance
async def get_detailed_plan_endpoint(params: RequestParams, request: Request):
    """获取详细计划 - 无限流版"""
    async def generate():
        try:
            params_dict = params.model_dump()
            async with optimizer.concurrent_limit():
                async for content in get_detailed_plan(params_dict):
                    yield {
                        "event": "message",
                        "data": json.dumps({"content": content, "type": "detailed_plan"})
                    }
            yield {
                "event": "success",
                "data": json.dumps({"message": "详细规划完成"})
            }
        except Exception as e:
            yield {
                "event": "error",
                "data": json.dumps({"error": str(e)})
            }

    return EventSourceResponse(generate())

if __name__ == "__main__":
    print("🚀 启动无限流版约会规划服务器...")
    print("⚠️  注意：此版本已禁用限流，请确保在安全环境中使用")
    print("访问地址: http://localhost:8000")
    print("性能监控: http://localhost:8000/metrics")

    uvicorn.run(
        "no_limit_server:app",
        host="0.0.0.0",
        port=8000,
        log_level="info",
        access_log=True,
        reload=False
    )
