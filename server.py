#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
FastAPI 服务器 - 约会规划服务
基于 main.py 中的功能创建 RESTful API 和 SSE 端点
"""

import asyncio
import json
from typing import Dict, Any
from fastapi import FastAPI, Request, HTTPException
from fastapi.responses import HTMLResponse, StreamingResponse
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from pydantic import BaseModel
from sse_starlette.sse import EventSourceResponse

# 导入原有的功能模块
from main import (
    explain_request,
    think_plan,
    search_purpose_location_info,
    get_detailed_plan
)

app = FastAPI(title="约会规划服务", description="基于 SSE 的约会规划 API")

# 挂载静态文件
app.mount("/static", StaticFiles(directory="static"), name="static")

# 模板配置
templates = Jinja2Templates(directory="templates")


# 请求模型定义
class DatingParams(BaseModel):
    to_location_province: str = "广东省"
    to_location_city: str = "深圳市"
    to_location_district: str = "宝安区"
    to_location_name: str = "西乡"

    user_lat_lng: str = "113.999706,22.588863"
    user_location_province: str = "广东省"
    user_location_city: str = "深圳市"
    user_location_district: str = "南山区"
    user_location_name: str = "塘朗城广场"

    departure_lat_lng: str = "113.999706,22.588863"
    departure_province: str = "广东省"
    departure_city: str = "深圳市"
    departure_district: str = "南山区"
    departure_name: str = "塘朗城广场"

    comp_lat_lng: str = "114.142917,22.620833"
    comp_location_province: str = "广东省"
    comp_location_city: str = "深圳市"
    comp_location_district: str = "龙岗区"
    comp_location_name: str = "怡乐花园"

    male_last_name: str = "张"
    female_last_name: str = "李"

    trans_tool_user: str = "bus"  # bus-公交 car-自驾
    trans_tool_comp: str = "car"  # bus-公交 car-自驾

    date: str = "2025-05-28"
    time_start: str = "09:00"
    time_end: str = "20:00"

    dating_times: int = 3  # 1-1次 2-2次 3-3多次 4-确认关系
    dating_type: str = "浪漫型"  # 浪漫型/趣味型/文艺型
    more_thoughts: str = "我希望约会时可以吃到火锅"


class RequestParams(BaseModel):
    user_input: str = "怎样避免约会的时候双方陷入沉默?"
    request_type: str = "dating"  # 'dating':约会规划，'chat':对话聊天
    dating_params: DatingParams


@app.get("/", response_class=HTMLResponse)
async def read_root(request: Request):
    """返回前端页面"""
    return templates.TemplateResponse("index.html", {"request": request})


@app.post("/explain-request")
async def explain_request_endpoint(params: RequestParams):
    """
    解释用户约会期望
    """
    try:
        params_dict = params.model_dump()
        result = explain_request(params_dict)
        return {"success": True, "data": result}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/think-plan")
async def think_plan_endpoint(params: RequestParams):
    """
    用户需求理解和任务拆分 - SSE 流式响应
    """
    async def generate():
        try:
            params_dict = params.model_dump()
            async for content in think_plan(params_dict):
                yield {
                    "event": "message",
                    "data": json.dumps({"content": content, "type": "think_plan"})
                }
            yield {
                "event": "end",
                "data": json.dumps({"message": "思考规划完成"})
            }
        except Exception as e:
            yield {
                "event": "error",
                "data": json.dumps({"error": str(e)})
            }

    return EventSourceResponse(generate())


@app.post("/search-location-info")
async def search_location_info_endpoint(params: RequestParams):
    """
    查询目的地信息(热门景点、美食、购物、娱乐照片及数量)
    """
    try:
        params_dict = params.model_dump()
        print(f"Debug: search_location_info params: {params_dict}")
        result = await search_purpose_location_info(params_dict)
        return {"success": True, "data": result}
    except Exception as e:
        import traceback
        print(f"Error in search_location_info_endpoint: {str(e)}")
        print(f"Traceback: {traceback.format_exc()}")


        raise HTTPException(status_code=500, detail=str(e))


@app.post("/get-detailed-plan")
async def get_detailed_plan_endpoint(params: RequestParams):
    """
    获取详细计划 - SSE 流式响应
    """
    async def generate():
        try:
            params_dict = params.model_dump()
            async for content in get_detailed_plan(params_dict):
                print(content)
                yield {
                    "event": "message",
                    "data": json.dumps({"content": content, "type": "detailed_plan"})
                }
            yield {
                "event": "end",
                "data": json.dumps({"message": "详细规划完成"})
            }
        except Exception as e:
            yield {
                "event": "error",
                "data": json.dumps({"error": str(e)})
            }

    return EventSourceResponse(generate())


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
