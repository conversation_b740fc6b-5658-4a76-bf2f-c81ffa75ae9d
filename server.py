#!/usr/bin/env python
# -*- coding: utf-8 -*-
'''
@File	:server.py
@Time	:2025/05/27 16:53:30
<AUTHOR>
@Mail	:<EMAIL>
'''

import asyncio
import json
from typing import Dict, Any
from fastapi import FastAPI, Request, HTTPException
from fastapi.responses import HTMLResponse, StreamingResponse
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from pydantic import BaseModel
from sse_starlette.sse import EventSourceResponse
from logger import LOGGER


# 导入原有的功能模块
from main import (
    explain_request,
    think_plan,
    search_purpose_location_info,
    get_detailed_plan
)

app = FastAPI(title="约会规划服务", description="基于 SSE 的约会规划 API")

# 挂载静态文件
app.mount("/static", StaticFiles(directory="static"), name="static")

# 模板配置
templates = Jinja2Templates(directory="templates")


# 请求模型定义
class DatingParams(BaseModel):
    to_location_province: str = "广东省"
    to_location_city: str = "深圳市"
    to_location_district: str = "宝安区"
    to_location_name: str = "西乡"

    user_lat_lng: str = "113.999706,22.588863"
    user_location_province: str = "广东省"
    user_location_city: str = "深圳市"
    user_location_district: str = "南山区"
    user_location_name: str = "塘朗城广场"

    departure_lat_lng: str = "113.999706,22.588863"
    departure_province: str = "广东省"
    departure_city: str = "深圳市"
    departure_district: str = "南山区"
    departure_name: str = "塘朗城广场"

    comp_lat_lng: str = "114.142917,22.620833"
    comp_location_province: str = "广东省"
    comp_location_city: str = "深圳市"
    comp_location_district: str = "龙岗区"
    comp_location_name: str = "怡乐花园"

    male_last_name: str = "张"
    female_last_name: str = "李"

    trans_tool_user: str = "bus"  # bus-公交 car-自驾
    trans_tool_comp: str = "car"  # bus-公交 car-自驾

    date: str = "2025-05-28"
    time_start: str = "09:00"
    time_end: str = "20:00"

    dating_times: int = 3  # 1-1次 2-2次 3-3多次 4-确认关系
    dating_type: str = "浪漫型"  # 浪漫型/趣味型/文艺型
    more_thoughts: str = "我希望约会时可以吃到火锅"


class RequestParams(BaseModel):
    user_input: str = "怎样避免约会的时候双方陷入沉默?"
    request_type: str = "dating"  # 'dating':约会规划，'chat':对话聊天
    dating_params: DatingParams


@app.get("/", response_class=HTMLResponse)
async def read_root(request: Request):
    """返回前端页面"""
    return templates.TemplateResponse("index.html", {"request": request})


@app.post("/explain-request")
async def explain_request_endpoint(params: RequestParams):
    """
    解释用户约会期望 - SSE 流式响应
    """
    async def generate():
        try:
            # 发送开始信号
            yield {
                "event": "info",
                "data": json.dumps({"content": "正在分析约会期望...\n", "type": "explain_request"})
            }

            await asyncio.sleep(0.1)  # 模拟处理时间

            params_dict = params.model_dump()
            result = explain_request(params_dict)

            # 发送结果
            yield {
                "event": "info",
                "data": json.dumps({"content": "分析完成", "type": "explain_request"})
            }
            yield {
                "event": "message",
                "data": json.dumps({"content": result, "type": "explain_request"})
            }

            yield {
                "event": "success",
                "data": json.dumps({"message": "约会期望解释完成"})
            }
        except Exception as e:
            yield {
                "event": "error",
                "data": json.dumps({"error": str(e)})
            }

    return EventSourceResponse(generate())


@app.post("/think-plan")
async def think_plan_endpoint(params: RequestParams):
    """
    用户需求理解和任务拆分 - SSE 流式响应
    """
    async def generate():
        try:
            params_dict = params.model_dump()
            async for content in think_plan(params_dict):
                yield {
                    "event": "message",
                    "data": json.dumps({"content": content, "type": "think_plan"})
                }
            yield {
                "event": "success",
                "data": json.dumps({"message": "思考规划完成", "type": "think_plan"})
            }
        except Exception as e:
            yield {
                "event": "error",
                "data": json.dumps({"error": str(e), "type": "think_plan"})
            }

    return EventSourceResponse(generate())


@app.post("/search-location-info")
async def search_location_info_endpoint(params: RequestParams):
    """
    查询目的地信息(热门景点、美食、购物、娱乐照片及数量) - SSE 流式响应
    """
    async def generate():
        try:
            # 发送开始信号
            yield {
                "event": "info",
                "data": json.dumps({"content": "开始查询目的地信息...\n", "type": "search_location_info"})
            }

            await asyncio.sleep(0.1)

            yield {
                "event": "info",
                "data": json.dumps({"content": "正在获取地理位置信息...\n", "type": "search_location_info"})
            }

            await asyncio.sleep(0.1)

            yield {
                "event": "info",
                "data": json.dumps({"content": "正在查询周边POI信息...\n", "type": "search_location_info"})
            }

            params_dict = params.model_dump()

            mock_data_sent = False
            try:
                result = await search_purpose_location_info(params_dict)
                final_result = {"success": True, "data": result}
            except Exception as e:
                import traceback
                LOGGER.error(f"Error in search_location_info_endpoint: {str(e)}")
                LOGGER.error(f"Traceback: {traceback.format_exc()}")

                # 如果是 API 密钥问题或其他错误，返回模拟数据
                # if "INVALID_USER_KEY" in str(e) or "api" in str(e).lower() or "NoneType" in str(e):
                #     print("检测到 API 密钥问题或数据错误，返回模拟数据")
                #     yield {
                #         "event": "message",
                #         "data": json.dumps({"content": "API密钥无效或数据错误，使用模拟数据...\n", "type": "search_location_info"})
                #     }

                #     mock_data = {
                #         'to_location_food': [
                #             {'name': '海底捞火锅', 'photos': ['photo1.jpg', 'photo2.jpg']},
                #             {'name': '星巴克咖啡', 'photos': ['photo3.jpg']},
                #             {'name': '哈根达斯', 'photos': ['photo4.jpg', 'photo5.jpg']},
                #         ],
                #         'to_location_shopping': [
                #             {'name': 'ZARA', 'photos': ['shop1.jpg']},
                #             {'name': '优衣库', 'photos': ['shop2.jpg']},
                #         ],
                #         'to_location_entertainment': [
                #             {'name': 'CGV影城', 'photos': ['cinema1.jpg']},
                #             {'name': 'KTV', 'photos': ['ktv1.jpg']},
                #         ],
                #         'to_location_sightseeing': [
                #             {'name': '中心公园', 'photos': ['park1.jpg']},
                #         ],
                #         'count': 7
                #     }
                #     final_result = {"success": True, "data": mock_data, "note": "使用模拟数据（API密钥无效或数据错误）"}

                #     # 发送模拟数据结果
                #     yield {
                #         "event": "message",
                #         "data": json.dumps({"content": json.dumps(final_result, ensure_ascii=False, indent=2), "type": "search_location_info"})
                #     }
                #     mock_data_sent = True
                # else:
                #     final_result = {"success": False, "error": str(e)}

            await asyncio.sleep(0.1)

            # 发送最终结果（只有在没有发送模拟数据时才发送）
            if not mock_data_sent:
                yield {
                    "event": "message",
                    "data": json.dumps({"content": json.dumps(final_result, ensure_ascii=False, indent=2), "type": "search_location_info"})
                }

            yield {
                "event": "success",
                "data": json.dumps({"message": "目的地信息查询完成"})
            }

        except Exception as e:
            import traceback
            LOGGER.error(f"Error in search_location_info_endpoint: {str(e)}")
            LOGGER.error(f"Traceback: {traceback.format_exc()}")
            yield {
                "event": "error",
                "data": json.dumps({"error": str(e)})
            }

    return EventSourceResponse(generate())


@app.post("/get-detailed-plan")
async def get_detailed_plan_endpoint(params: RequestParams):
    """
    获取详细计划 - SSE 流式响应
    """
    async def generate():
        try:
            params_dict = params.model_dump()
            async for content in get_detailed_plan(params_dict):
                LOGGER.info(content)
                yield {
                    "event": "message",
                    "data": json.dumps({"content": content, "type": "detailed_plan"})
                }
            yield {
                "event": "success",
                "data": json.dumps({"message": "详细规划完成"})
            }
        except Exception as e:
            import traceback
            LOGGER.error(f"Error in get_detailed_plan_endpoint: {str(e)}")
            LOGGER.error(f"Traceback: {traceback.format_exc()}")
            yield {
                "event": "error",
                "data": json.dumps({"error": str(e)})
            }

    return EventSourceResponse(generate())


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
