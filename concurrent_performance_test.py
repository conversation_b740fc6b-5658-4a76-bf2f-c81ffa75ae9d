#!/usr/bin/env python3
"""
约会规划服务并发性能测试
使用指定参数进行极限并发测试
"""

import asyncio
import aiohttp
import json
import time
import statistics
import psutil
import sys
from datetime import datetime
from typing import List, Dict, Any

class PerformanceTestResult:
    def __init__(self):
        self.total_requests = 0
        self.successful_requests = 0
        self.failed_requests = 0
        self.response_times = []
        self.errors = []
        self.start_time = None
        self.end_time = None
        self.content_samples = []
        self.cpu_usage = []
        self.memory_usage = []

class ConcurrentTester:
    def __init__(self, base_url="http://localhost:8000"):
        self.base_url = base_url
        self.test_params = {
            'user_input': '怎样避免约会的时候双方陷入沉默?',
            'request_type': 'dating',
            'dating_params': {
                'to_location_province': '广东省',
                'to_location_city': '深圳市',
                'to_location_district': '宝安区',
                'to_location_name': '西乡',
                'user_lat_lng': '113.999706,22.588863',
                'user_location_province': '广东省',
                'user_location_city': '深圳市',
                'user_location_district': '南山区',
                'user_location_name': '塘朗城广场',
                'departure_lat_lng': '113.999706,22.588863',
                'departure_province': '广东省',
                'departure_city': '深圳市',
                'departure_district': '南山区',
                'departure_name': '塘朗城广场',
                'comp_lat_lng': '114.142917,22.620833',
                'comp_location_province': '广东省',
                'comp_location_city': '深圳市',
                'comp_location_district': '龙岗区',
                'comp_location_name': '怡乐花园',
                'male_last_name': '张',
                'female_last_name': '李',
                'trans_tool_user': 'bus',
                'trans_tool_comp': 'car',
                'date': '2025-05-30',
                'time_start': '09:00',
                'time_end': '20:00',
                'dating_times': 3,
                'dating_type': '浪漫型',
                'more_thoughts': '我希望约会时可以吃到火锅'
            }
        }

    async def single_request_test(self, session: aiohttp.ClientSession, request_id: int) -> Dict[str, Any]:
        """执行单个请求测试"""
        start_time = time.time()
        result = {
            'request_id': request_id,
            'success': False,
            'response_time': 0,
            'error': None,
            'content_length': 0,
            'content_sample': '',
            'status_code': 0
        }
        
        try:
            timeout = aiohttp.ClientTimeout(total=60)
            async with session.post(
                f"{self.base_url}/get-detailed-plan",
                json=self.test_params,
                timeout=timeout
            ) as response:
                result['status_code'] = response.status
                
                if response.status == 200:
                    content_parts = []
                    line_count = 0
                    
                    async for line in response.content:
                        line_count += 1
                        if line:
                            line_str = line.decode('utf-8').strip()
                            
                            if line_str.startswith('data: '):
                                data_str = line_str[6:]
                                try:
                                    data = json.loads(data_str)
                                    
                                    if 'error' in data:
                                        result['error'] = data['error']
                                        break
                                    elif 'content' in data:
                                        content = data['content']
                                        if content:
                                            content_parts.append(str(content))
                                            
                                except json.JSONDecodeError:
                                    continue
                        
                        # 限制读取行数以避免超时
                        if line_count > 100:
                            break
                    
                    if content_parts and not result['error']:
                        full_content = ''.join(content_parts)
                        result['content_length'] = len(full_content)
                        result['content_sample'] = full_content[:200] + '...' if len(full_content) > 200 else full_content
                        result['success'] = True
                    elif result['error']:
                        result['success'] = False
                    else:
                        result['error'] = "No content received"
                        result['success'] = False
                else:
                    text = await response.text()
                    result['error'] = f"HTTP {response.status}: {text[:100]}"
                    result['success'] = False
                    
        except asyncio.TimeoutError:
            result['error'] = "Request timeout"
        except Exception as e:
            result['error'] = str(e)
        
        result['response_time'] = time.time() - start_time
        return result

    async def concurrent_test(self, concurrent_users: int, duration_seconds: int = 30) -> PerformanceTestResult:
        """执行并发测试"""
        print(f"\n🚀 开始并发测试: {concurrent_users} 并发用户, 持续 {duration_seconds} 秒")
        
        result = PerformanceTestResult()
        result.start_time = time.time()
        
        # 创建连接池
        connector = aiohttp.TCPConnector(limit=concurrent_users * 2, limit_per_host=concurrent_users * 2)
        timeout = aiohttp.ClientTimeout(total=60)
        
        async with aiohttp.ClientSession(connector=connector, timeout=timeout) as session:
            tasks = []
            request_id = 0
            
            # 启动系统监控
            monitor_task = asyncio.create_task(self.monitor_system_resources(result, duration_seconds))
            
            # 持续发送请求
            end_time = time.time() + duration_seconds
            
            while time.time() < end_time:
                # 启动一批并发请求
                batch_tasks = []
                for _ in range(concurrent_users):
                    if time.time() >= end_time:
                        break
                    task = asyncio.create_task(self.single_request_test(session, request_id))
                    batch_tasks.append(task)
                    request_id += 1
                
                if batch_tasks:
                    # 等待这批请求完成
                    batch_results = await asyncio.gather(*batch_tasks, return_exceptions=True)
                    
                    for batch_result in batch_results:
                        if isinstance(batch_result, dict):
                            result.total_requests += 1
                            result.response_times.append(batch_result['response_time'])
                            
                            if batch_result['success']:
                                result.successful_requests += 1
                                if len(result.content_samples) < 3:  # 保存前3个成功响应的内容样本
                                    result.content_samples.append(batch_result['content_sample'])
                            else:
                                result.failed_requests += 1
                                result.errors.append(batch_result['error'])
                        else:
                            result.total_requests += 1
                            result.failed_requests += 1
                            result.errors.append(str(batch_result))
                
                # 短暂等待避免过度压力
                await asyncio.sleep(0.1)
            
            # 停止监控
            monitor_task.cancel()
            try:
                await monitor_task
            except asyncio.CancelledError:
                pass
        
        result.end_time = time.time()
        return result

    async def monitor_system_resources(self, result: PerformanceTestResult, duration: int):
        """监控系统资源使用情况"""
        try:
            for _ in range(duration * 2):  # 每0.5秒采样一次
                cpu_percent = psutil.cpu_percent(interval=0.1)
                memory_info = psutil.virtual_memory()
                
                result.cpu_usage.append(cpu_percent)
                result.memory_usage.append(memory_info.percent)
                
                await asyncio.sleep(0.5)
        except asyncio.CancelledError:
            pass

    def generate_report(self, results: List[PerformanceTestResult], concurrent_levels: List[int]) -> str:
        """生成详细的测试报告"""
        report = []
        report.append("=" * 80)
        report.append("🎯 约会规划服务并发性能测试报告")
        report.append("=" * 80)
        report.append(f"📅 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append(f"🖥️  测试环境: {sys.platform}")
        report.append(f"💾 系统内存: {psutil.virtual_memory().total / (1024**3):.1f} GB")
        report.append(f"🔧 CPU核心数: {psutil.cpu_count()}")
        report.append("")
        
        for i, (result, concurrent_users) in enumerate(zip(results, concurrent_levels)):
            duration = result.end_time - result.start_time if result.end_time and result.start_time else 0
            
            report.append(f"📊 测试场景 {i+1}: {concurrent_users} 并发用户")
            report.append("-" * 50)
            report.append(f"⏱️  测试持续时间: {duration:.2f} 秒")
            report.append(f"📈 总请求数: {result.total_requests}")
            report.append(f"✅ 成功请求: {result.successful_requests}")
            report.append(f"❌ 失败请求: {result.failed_requests}")
            report.append(f"📊 成功率: {(result.successful_requests/result.total_requests*100):.2f}%" if result.total_requests > 0 else "0%")
            
            if result.response_times:
                report.append(f"⚡ 平均响应时间: {statistics.mean(result.response_times):.3f} 秒")
                report.append(f"🚀 最快响应时间: {min(result.response_times):.3f} 秒")
                report.append(f"🐌 最慢响应时间: {max(result.response_times):.3f} 秒")
                report.append(f"📊 响应时间中位数: {statistics.median(result.response_times):.3f} 秒")
                
                if len(result.response_times) > 1:
                    report.append(f"📈 响应时间标准差: {statistics.stdev(result.response_times):.3f} 秒")
            
            if result.cpu_usage:
                report.append(f"💻 平均CPU使用率: {statistics.mean(result.cpu_usage):.1f}%")
                report.append(f"💻 最高CPU使用率: {max(result.cpu_usage):.1f}%")
            
            if result.memory_usage:
                report.append(f"💾 平均内存使用率: {statistics.mean(result.memory_usage):.1f}%")
                report.append(f"💾 最高内存使用率: {max(result.memory_usage):.1f}%")
            
            if result.total_requests > 0 and duration > 0:
                rps = result.total_requests / duration
                successful_rps = result.successful_requests / duration
                report.append(f"🔥 总QPS (每秒请求数): {rps:.2f}")
                report.append(f"✅ 成功QPS: {successful_rps:.2f}")
            
            # 显示错误统计
            if result.errors:
                error_counts = {}
                for error in result.errors:
                    error_str = str(error)[:50]  # 截断长错误信息
                    error_counts[error_str] = error_counts.get(error_str, 0) + 1
                
                report.append("❌ 错误统计:")
                for error, count in sorted(error_counts.items(), key=lambda x: x[1], reverse=True)[:5]:
                    report.append(f"   • {error}: {count} 次")
            
            # 显示内容样本
            if result.content_samples:
                report.append("📄 响应内容样本:")
                for j, sample in enumerate(result.content_samples[:2]):  # 只显示前2个样本
                    report.append(f"   样本 {j+1}: {sample}")
            
            report.append("")
        
        # 性能总结
        report.append("🎯 性能总结")
        report.append("-" * 50)
        
        max_successful_concurrent = 0
        max_successful_qps = 0
        
        for i, (result, concurrent_users) in enumerate(zip(results, concurrent_levels)):
            if result.successful_requests > 0:
                duration = result.end_time - result.start_time if result.end_time and result.start_time else 1
                success_rate = result.successful_requests / result.total_requests * 100
                qps = result.successful_requests / duration
                
                if success_rate >= 95:  # 95%以上成功率认为是可接受的
                    max_successful_concurrent = max(max_successful_concurrent, concurrent_users)
                    max_successful_qps = max(max_successful_qps, qps)
        
        report.append(f"🏆 最大支持并发数: {max_successful_concurrent} (成功率≥95%)")
        report.append(f"🚀 最大成功QPS: {max_successful_qps:.2f}")
        
        # 推荐配置
        report.append("")
        report.append("💡 推荐配置")
        report.append("-" * 50)
        recommended_concurrent = max(1, int(max_successful_concurrent * 0.8))
        report.append(f"🎯 推荐并发数: {recommended_concurrent} (安全余量20%)")
        report.append(f"⚡ 预期QPS: {max_successful_qps * 0.8:.2f}")
        
        return "\n".join(report)

async def main():
    """主测试函数"""
    tester = ConcurrentTester()
    
    # 测试不同的并发级别
    concurrent_levels = [1, 2, 5, 10, 15, 20, 25, 30]
    test_duration = 20  # 每个级别测试20秒
    
    print("🎯 约会规划服务并发性能测试")
    print("=" * 50)
    print(f"📋 测试计划: {len(concurrent_levels)} 个并发级别")
    print(f"⏱️  每个级别测试时长: {test_duration} 秒")
    print(f"🎯 并发级别: {concurrent_levels}")
    
    results = []
    
    for concurrent_users in concurrent_levels:
        try:
            result = await tester.concurrent_test(concurrent_users, test_duration)
            results.append(result)
            
            # 实时显示结果
            success_rate = (result.successful_requests / result.total_requests * 100) if result.total_requests > 0 else 0
            duration = result.end_time - result.start_time if result.end_time and result.start_time else 1
            qps = result.successful_requests / duration
            
            print(f"✅ {concurrent_users} 并发: 成功率 {success_rate:.1f}%, QPS {qps:.2f}, 总请求 {result.total_requests}")
            
            # 如果成功率太低，提前结束测试
            if success_rate < 50 and concurrent_users > 5:
                print(f"⚠️  成功率过低 ({success_rate:.1f}%)，停止更高并发测试")
                break
                
        except Exception as e:
            print(f"❌ 测试 {concurrent_users} 并发时发生错误: {e}")
            break
    
    # 生成并显示报告
    if results:
        report = tester.generate_report(results, concurrent_levels[:len(results)])
        print("\n" + report)
        
        # 保存报告到文件
        with open("performance_test_report.txt", "w", encoding="utf-8") as f:
            f.write(report)
        print(f"\n📄 详细报告已保存到: performance_test_report.txt")
    else:
        print("❌ 没有成功的测试结果")

if __name__ == "__main__":
    asyncio.run(main())
