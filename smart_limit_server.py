#!/usr/bin/env python3
"""
智能限流版本的约会规划服务器
根据服务器负载和用户行为动态调整限流
"""

import asyncio
import json
import time
import psutil
from typing import Dict, Any
import uvicorn
from fastapi import FastAPI, Request
from fastapi.responses import HTMLResponse
from fastapi.staticfiles import StaticFiles
from sse_starlette.sse import EventSourceResponse

# 导入业务逻辑
from dating_agent import (
    explain_request, think_plan,
    search_purpose_location_info, get_detailed_plan,
    RequestParams
)

from performance_optimizer import PerformanceOptimizer

app = FastAPI(title="约会规划服务 - 智能限流版", version="3.0.0")

class SmartRateLimiter:
    def __init__(self):
        self.user_requests = {}  # 用户请求历史
        self.server_load_history = []  # 服务器负载历史
        self.backend_clients = set()  # 已知的后端服务IP

    def get_server_load(self):
        """获取服务器负载"""
        cpu_percent = psutil.cpu_percent(interval=0.1)
        memory_percent = psutil.virtual_memory().percent
        return {"cpu": cpu_percent, "memory": memory_percent}

    def calculate_dynamic_limit(self, endpoint: str) -> int:
        """根据服务器负载动态计算限流阈值"""
        load = self.get_server_load()

        # 基础限流值
        base_limits = {
            "explain-request": 100,
            "think-plan": 50,
            "search-location-info": 80,
            "get-detailed-plan": 30
        }

        base_limit = base_limits.get(endpoint, 50)

        # 根据服务器负载调整
        if load["cpu"] < 50 and load["memory"] < 70:
            # 服务器负载低，放宽限制
            multiplier = 3.0
        elif load["cpu"] < 70 and load["memory"] < 80:
            # 服务器负载中等，正常限制
            multiplier = 2.0
        else:
            # 服务器负载高，严格限制
            multiplier = 0.5

        return int(base_limit * multiplier)

    def is_trusted_user(self, client_id: str) -> bool:
        """判断是否为可信用户（基于历史行为）"""
        if client_id not in self.user_requests:
            return False

        user_history = self.user_requests[client_id]

        # 如果用户历史请求都很正常（没有突发大量请求），则认为是可信用户
        if len(user_history) > 10:
            recent_requests = user_history[-10:]
            time_spans = [recent_requests[i+1] - recent_requests[i] for i in range(len(recent_requests)-1)]
            avg_interval = sum(time_spans) / len(time_spans) if time_spans else 0

            # 平均请求间隔大于5秒的用户认为是正常用户
            return avg_interval > 5

        return False

    def should_allow_request(self, client_id: str, endpoint: str) -> tuple[bool, str]:
        """智能判断是否允许请求"""
        now = time.time()

        # 初始化用户请求历史
        if client_id not in self.user_requests:
            self.user_requests[client_id] = []

        # 清理60秒前的请求记录
        self.user_requests[client_id] = [
            req_time for req_time in self.user_requests[client_id]
            if now - req_time < 60
        ]

        # 获取动态限流阈值
        dynamic_limit = self.calculate_dynamic_limit(endpoint)

        # 可信用户获得额外配额
        if self.is_trusted_user(client_id):
            dynamic_limit = int(dynamic_limit * 1.5)

        current_requests = len(self.user_requests[client_id])

        if current_requests >= dynamic_limit:
            load = self.get_server_load()
            return False, f"请求频率过高，请稍后再试。当前限制: {dynamic_limit}/分钟 (CPU: {load['cpu']:.1f}%, 内存: {load['memory']:.1f}%)"

        # 记录请求
        self.user_requests[client_id].append(now)
        return True, ""

# 初始化智能限流器和优化器
smart_limiter = SmartRateLimiter()
optimizer = PerformanceOptimizer()

def monitor_performance(func):
    async def wrapper(*args, **kwargs):
        start_time = time.time()
        try:
            result = await func(*args, **kwargs)
            duration = time.time() - start_time
            print(f"✅ {func.__name__} 执行成功，耗时: {duration:.2f}s")
            return result
        except Exception as e:
            duration = time.time() - start_time
            print(f"❌ {func.__name__} 执行失败，耗时: {duration:.2f}s，错误: {str(e)}")
            raise
    return wrapper

# 静态文件和模板
app.mount("/static", StaticFiles(directory="static"), name="static")

@app.get("/", response_class=HTMLResponse)
async def read_root():
    with open("templates/index.html", "r", encoding="utf-8") as f:
        return HTMLResponse(content=f.read())

@app.get("/health")
async def health_check():
    load = smart_limiter.get_server_load()
    return {
        "status": "healthy",
        "timestamp": time.time(),
        "server_load": load,
        "rate_limiting": "smart"
    }

@app.get("/metrics")
async def get_metrics():
    load = smart_limiter.get_server_load()
    return {
        "performance": {
            "cache_size": len(optimizer.cache),
            "concurrent_limit": optimizer.max_concurrent,
            "server_load": load,
            "active_users": len(smart_limiter.user_requests),
            "rate_limiting": "smart"
        },
        "timestamp": time.time()
    }

async def smart_rate_limit_check(client_id: str, endpoint: str):
    """智能限流检查"""
    allowed, message = smart_limiter.should_allow_request(client_id, endpoint)
    if not allowed:
        yield {
            "event": "error",
            "data": json.dumps({"error": message})
        }
        return False
    return True

@app.post("/explain-request")
@monitor_performance
async def explain_request_endpoint(params: RequestParams, request: Request):
    """约会期望解释 - 智能限流版"""
    client_id = request.client.host

    async def generate():
        try:
            # 智能限流检查
            async for result in smart_rate_limit_check(client_id, "explain-request"):
                if isinstance(result, dict):
                    yield result
                    return

            yield {
                "event": "info",
                "data": json.dumps({"content": "正在分析约会期望...\n", "type": "explain_request"})
            }

            await asyncio.sleep(0.1)
            params_dict = params.model_dump()

            # 使用缓存
            cache_key = f"explain:{hash(str(params_dict))}"
            cached_result = await optimizer._get_cache(cache_key)

            if cached_result:
                result = cached_result
            else:
                result = explain_request(params_dict)
                await optimizer._set_cache(cache_key, result, 1800)

            yield {
                "event": "message",
                "data": json.dumps({"content": result, "type": "explain_request"})
            }

            yield {
                "event": "success",
                "data": json.dumps({"message": "约会期望解释完成"})
            }
        except Exception as e:
            yield {
                "event": "error",
                "data": json.dumps({"error": str(e)})
            }

    return EventSourceResponse(generate())

if __name__ == "__main__":
    print("🧠 启动智能限流版约会规划服务器...")
    print("✨ 特性：根据服务器负载和用户行为动态调整限流")
    print("访问地址: http://localhost:8000")
    print("性能监控: http://localhost:8000/metrics")

    uvicorn.run(
        "smart_limit_server:app",
        host="0.0.0.0",
        port=8000,
        log_level="info",
        access_log=True,
        reload=False
    )
