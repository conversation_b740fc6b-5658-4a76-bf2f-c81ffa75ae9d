#!/usr/bin/env python
# -*- coding: utf-8 -*-
'''
@File	:tools.py
@Time	:2025/05/14 11:59:32
<AUTHOR>
@Mail	:<EMAIL>
'''


from cinema_service import search_movies
from prompt import FOOD_PROMPT, WEATHER_PROMPT, TRAFFIC_PROMPT
from llm import sync_client, async_client
from location_helper import LocationHelper
from langchain_core.tools import BaseTool, tool
from pydantic import BaseModel, Field
from logger import LOGGER
from abc import ABC, abstractmethod
from typing import Optional, Dict, Any, List, Type
import asyncio
import os
import pretty_errors
os.system('clear')
pretty_errors.configure(
    separator_character='*',
    filename_display=pretty_errors.FILENAME_EXTENDED,
    line_number_first=True,
    display_link=True,
    lines_before=5,
    lines_after=2,
    line_color=pretty_errors.RED+'> '+pretty_errors.BRIGHT_RED,
    filename_color=pretty_errors.YELLOW,
    header_color=pretty_errors.BRIGHT_GREEN,
    link_color=pretty_errors.BRIGHT_BLUE,
    code_color=pretty_errors.BRIGHT_RED,
    # code_color='  '+pretty_errors.default_config.line_color
    line_number_color=pretty_errors.BRIGHT_RED
)


# 基础输入模型
class BaseInput(BaseModel):
    """所有工具输入的基类"""
    pass

# 目的地周边POI
# POI查询输入模型


class PoiInput(BaseInput):
    # location_helper = LocationHelper()
    #     to_location_detail_info = await location_helper.extract_to_location_detail(
    #         params_dict['user_input'], params_dict['user_lat_lng'])
    #     to_location_detail = to_location_detail_info['to_location_detail']
    #     # to_location_adcode = to_location_detail_info['to_location_adcode']

    #     to_location_lat_lng = await location_helper.to_location2lat_lng(to_location_detail)['location']

    model_config = {"arbitrary_types_allowed": True}
    location_helper: LocationHelper = Field(
        description='Parameter defines the location helper. e.g. LocationHelper()')
    to_location_detail: str = Field(
        description='Parameter defines the detailed address of the destination. e.g. "深圳市福田区COCO Park"')
    # to_location_lat_lng
    to_location_lat_lng: str = Field(
        description='Parameter defines the location of the destination. e.g. "114.055198,22.520922" (COCO Park)')


class PoiInputSchema(BaseModel):
    params: PoiInput


# 天气查询输入模型
class WeatherInput(BaseInput):
    location: str = Field(
        description='Parameter defines the location for weather query. e.g. 深圳')
    date: Optional[str] = Field(
        default=None, description='Parameter defines the date for weather query. e.g. 2025-05-17')
    to_location_adcode: str = Field(
        description='Parameter defines the location for weather query. e.g. 440305')


class WeatherInputSchema(BaseModel):
    params: WeatherInput


# 电影查询输入模型
# class MoviesInput(BaseInput):
#     to_location: str = Field(
#         default='COCO Park', description='Parameter defines the destination location of dating. e.g. COCO Park')
#     to_location_lat: float = Field(
#         default=22.520922, description='Parameter defines the latitude of the destination location. e.g. 22.520922')
#     to_location_lng: float = Field(
#         default=114.055198, description='Parameter defines the longitude of the destination location. e.g. 114.055198')
class MoviesInput(BaseInput):
    to_location: str = Field(
        description='Parameter defines the destination location of dating. e.g. COCO Park')
    to_location_lat: float = Field(
        description='Parameter defines the latitude of the destination location. e.g. 22.520922')
    to_location_lng: float = Field(
        description='Parameter defines the longitude of the destination location. e.g. 114.055198')
    watch_movie_date: str = Field(
        description='Parameter defines the date of the movie to watch. e.g. 2025-05-12')


class MoviesInputSchema(BaseModel):
    params: MoviesInput


# 交通查询输入模型
class TrafficInput(BaseInput):
    # origin: str, destination: str, mode: str = 'bus', city: str = '', city_d: str = ''
    origin: Optional[str] = Field(
        description='Parameter defines the origin location. e.g. "116.481028,39.989643"')
    destination: Optional[str] = Field(
        description='Parameter defines the destination location. e.g. "116.434446,39.90816"')
    mode: Optional[str] = Field(
        description='Parameter defines the mode of transportation. e.g. "bus"')
    city: Optional[str] = Field(
        description='Parameter defines the city of origin. e.g. "深圳"')
    city_d: Optional[str] = Field(
        description='Parameter defines the city of destination. e.g. "深圳"')


class TrafficInputSchema(BaseModel):
    params: TrafficInput


# 工具抽象基类
class ToolBase(ABC):
    """所有工具的抽象基类"""
    @abstractmethod
    async def execute(self, params: Any) -> str:
        """执行工具的方法"""
        pass


# 目的地POI查询工具实现
class PoiTool(ToolBase):
    async def execute(self, params: PoiInput) -> str:
        """
        执行目的地POI查询
        """
        params_dict = {
            # 'user_lat_lng': params.user_lat_lng,
            # 'user_input': params.user_input,
            'location_helper': params.location_helper,
            'to_location_detail': params.to_location_detail,
            'to_location_lat_lng': params.to_location_lat_lng,
        }

        # location_helper = LocationHelper()
        # to_location_detail_info = await location_helper.extract_to_location_detail(
        #     params_dict['user_input'], params_dict['user_lat_lng'])
        # to_location_detail = to_location_detail_info['to_location_detail']
        # to_location_adcode = to_location_detail_info['to_location_adcode']

        # to_location_lat_lng = await location_helper.to_location2lat_lng(to_location_detail)['location']
        # to_location_province = await location_helper.to_location2lat_lng(to_location_detail)['province']
        # to_location_city = await location_helper.to_location2lat_lng(to_location_detail)['city']
        location_helper = params_dict['location_helper']
        to_location_detail = params_dict['to_location_detail']
        to_location_lat_lng = params_dict['to_location_lat_lng']

        to_location_food_task = location_helper.nearby_poi_search(
            to_location_lat_lng, poi_types='050000', radius=600, offset=19)
        to_location_shopping_task = location_helper.nearby_poi_search(
            to_location_lat_lng, poi_types='060100', radius=700, offset=3)
        to_location_entertainment_task = location_helper.nearby_poi_search(
            to_location_lat_lng, poi_types='080000', radius=900, offset=15)

        to_location_sightseeing_task = location_helper.nearby_poi_search(
            to_location_lat_lng, poi_types='110000', radius=1000, offset=10)

        results = await asyncio.gather(
            to_location_food_task,
            to_location_shopping_task,
            to_location_entertainment_task,
            to_location_sightseeing_task
        )

        # print(';;;;;;;;;;;;;;;;;;;;')
        # print(results)
        # import sys
        # sys.exit()
        if results[0]['status'] == '1':
            to_location_food = results[0]['pois']
        else:
            to_location_food = await location_helper.nearby_poi_search(to_location_lat_lng, poi_types='050000', radius=600, offset=19)
        if results[1]['status'] == '1':
            to_location_shopping = results[1]['pois']
        else:
            to_location_shopping = await location_helper.nearby_poi_search(to_location_lat_lng, poi_types='060100', radius=700, offset=3)
        if results[2]['status'] == '1':
            to_location_entertainment = results[2]['pois']
        else:
            to_location_entertainment = await location_helper.nearby_poi_search(to_location_lat_lng, poi_types='080000', radius=900, offset=15)
        if results[3]['status'] == '1':
            to_location_sightseeing = results[3]['pois']
        else:
            to_location_sightseeing = await location_helper.nearby_poi_search(to_location_lat_lng, poi_types='110000', radius=1000, offset=10)

        # to_location_shopping = results[1]['pois']
        # to_location_entertainment = results[2]['pois']
        # to_location_sightseeing = results[3]['pois']

        return {
            'to_location_food': to_location_food,
            'to_location_shopping': to_location_shopping,
            'to_location_entertainment': to_location_entertainment,
            'to_location_sightseeing': to_location_sightseeing,
        }


# 天气查询工具实现
class WeatherTool(ToolBase):
    async def execute(self, params: WeatherInput) -> str:
        """
        执行天气查询
        """
        params_dict = {
            'location': params.location,
            'date': params.date,
            'to_location_adcode': params.to_location_adcode,
        }

        location_helper = LocationHelper()
        gaode_weather_info = await location_helper.weather_search(to_location_adcode=params_dict['to_location_adcode'])

        # 检查天气查询是否成功
        if isinstance(gaode_weather_info, dict) and gaode_weather_info.get('status') == 'error':
            LOGGER.warning(f'高德天气查询失败: {gaode_weather_info.get("message", "未知错误")}，使用websearch')
            completion = await async_client.chat.completions.create(
                model="qwen-plus",
                messages=[{'role': 'system', 'content': WEATHER_PROMPT},
                          {'role': 'user', 'content': f'{params_dict["location"]}{params_dict["date"]}的天气怎么样？'}],
                extra_body={
                    "enable_search": True
                }
            )
            return completion.choices[0].message.content

        # 检查是否为有效的天气数据格式
        if not isinstance(gaode_weather_info, list) or len(gaode_weather_info) == 0:
            LOGGER.warning(f'高德天气数据格式异常: {gaode_weather_info}，使用websearch')
            completion = await async_client.chat.completions.create(
                model="qwen-plus",
                messages=[{'role': 'system', 'content': WEATHER_PROMPT},
                          {'role': 'user', 'content': f'{params_dict["location"]}{params_dict["date"]}的天气怎么样？'}],
                extra_body={
                    "enable_search": True
                }
            )
            return completion.choices[0].message.content

        # 检查日期是否在预报范围内
        try:
            is_date_in_casts = any(cast['date'] == params_dict['date']
                                   for cast in gaode_weather_info[0]['casts'])
        except (KeyError, IndexError, TypeError) as e:
            LOGGER.warning(f'解析天气数据失败: {e}，使用websearch')
            completion = await async_client.chat.completions.create(
                model="qwen-plus",
                messages=[{'role': 'system', 'content': WEATHER_PROMPT},
                          {'role': 'user', 'content': f'{params_dict["location"]}{params_dict["date"]}的天气怎么样？'}],
                extra_body={
                    "enable_search": True
                }
            )
            return completion.choices[0].message.content

        if not is_date_in_casts:
            LOGGER.info(f'请求的日期{params_dict["date"]}不在高德casts范围内,使用websearch')

            completion = await async_client.chat.completions.create(
                model="qwen-plus",
                messages=[{'role': 'system', 'content': WEATHER_PROMPT},
                          {'role': 'user', 'content': f'{params_dict["location"]}{params_dict["date"]}的天气怎么样？'}],
                extra_body={
                    "enable_search": True
                }
            )
            return completion.choices[0].message.content
        else:
            return gaode_weather_info


# 交通查询工具实现
class TrafficTool(ToolBase):
    async def execute(self, params: TrafficInput) -> str:
        """
        执行交通查询
        """
        params_dict = {
            'origin': params.origin,
            'destination': params.destination,
            'mode': params.mode,
            'city': params.city,
            'city_d': params.city_d,
        }

        location_helper = LocationHelper()
        result = await location_helper.traffic_plan(**params_dict)
        return result


# 电影查询工具实现
class MoviesTool(ToolBase):
    async def execute(self, params: MoviesInput) -> dict:
        """
        执行电影查询
        """
        params_dict = {
            'to_location': params.to_location,
            'to_location_lat': params.to_location_lat,
            'to_location_lng': params.to_location_lng,
            'watch_movie_date': params.watch_movie_date,
        }
        result = await search_movies(**params_dict)
        return result


# 工具工厂类
class ToolFactory:
    """工具工厂，负责创建各种工具实例"""
    _tools: Dict[str, Type[ToolBase]] = {
        'poi': PoiTool,
        'weather': WeatherTool,
        'traffic': TrafficTool,
        'movies': MoviesTool,
    }

    @classmethod
    def get_tool(cls, tool_name: str) -> ToolBase:
        """获取工具实例"""
        if tool_name not in cls._tools:
            raise ValueError(f"Tool {tool_name} not found")
        return cls._tools[tool_name]()


# LangChain工具定义
# 目的地POI查询工具
@tool(args_schema=PoiInputSchema)
async def poi_seacher(params: PoiInput) -> str:
    '''
    查询目的地位置周边的的POI信息。

    Returns:
        dict: POI查询结果，包含周边的餐厅、购物中心、娱乐场所和景点信息。
    '''
    tool = ToolFactory.get_tool('poi')
    result = await tool.execute(params)
    LOGGER.info(result)
    return result


# 天气查询工具
@tool(args_schema=WeatherInputSchema)
async def weather_finder(params: WeatherInput) -> str:
    '''
    查询指定位置的天气信息。

    Returns:
        str: 天气查询结果
    '''
    tool = ToolFactory.get_tool('weather')
    result = await tool.execute(params)
    LOGGER.info(result)
    return result


# 交通查询工具
@tool(args_schema=TrafficInputSchema)
async def traffic_finder(params: TrafficInput) -> str:
    '''
    查询从出发地到目的地的最佳出行路线。

    Returns:
        str: 交通查询结果，包含预计时间、距离、交通方式等信息。
    '''
    tool = ToolFactory.get_tool('traffic')
    result = await tool.execute(params)
    LOGGER.info(result)
    return result


# 电影查询工具
@tool(args_schema=MoviesInputSchema)
async def movies_finder(params: MoviesInput) -> dict:
    '''
    查询指定位置附近的电影院和正在热映的电影。

    Returns:
        dict: 电影院和电影查询结果，包含影院名称、地址、电话和正在热映的电影信息。
    '''
    tool = ToolFactory.get_tool('movies')
    result = await tool.execute(params)
    LOGGER.info(result)
    return result


async def run_tools():
    # 准备输入参数
    # poi_input = {"params": PoiInput(user_lat_lng='113.999706,22.588863', user_input='这周六下午我想和女朋友在COCO Park约会')}
    # poi_input = {"params": PoiInput(
    #     user_lat_lng='113.999706,22.588863', user_input='这周六下午我想和女朋友在COCO Park约会')}
    location_helper = LocationHelper()
    poi_input = {"params": PoiInput(
        location_helper=location_helper,
        to_location_detail='深圳市福田区COCO Park',
        to_location_lat_lng='114.055198,22.520922'
    )}
    weather_input = {"params": WeatherInput(
        location='深圳', date='2025-05-21', to_location_adcode='440305')}
    traffic_input = {"params": TrafficInput(
        origin='116.481028,39.989643', destination='116.434446,39.90816', mode='bus', city='深圳', city_d='深圳'
    )}
    movies_input = {"params": MoviesInput(
        to_location='COCO Park',
        to_location_lat=22.520922,
        to_location_lng=114.055198,
        watch_movie_date='2025-05-21'  # 添加日期参数
    )}

    # 使用 gather 并行执行所有查询
    print("===== 开始并行查询 =====\n")
    tasks = [
        poi_seacher.ainvoke(poi_input),
        weather_finder.ainvoke(weather_input),
        traffic_finder.ainvoke(traffic_input),
        movies_finder.ainvoke(movies_input)
    ]

    # 等待所有任务完成
    results = await asyncio.gather(*tasks)

    # 打印结果
    print("\n===== POI查询结果 =====")
    print(results[0])

    print("\n===== 天气查询结果 =====")
    print(results[1])

    print("\n===== 交通查询结果 =====")
    print(results[2])

    print("\n===== 电影查询结果 =====")
    print(results[3])


if __name__ == "__main__":
    asyncio.run(run_tools())
