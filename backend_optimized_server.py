#!/usr/bin/env python3
"""
专为Java后端调用优化的约会规划服务器
解决后端代理模式下的限流问题
"""

import asyncio
import json
import time
import psutil
from typing import Dict, Any
import uvicorn
from fastapi import FastAPI, Request, Header
from fastapi.responses import HTMLResponse
from fastapi.staticfiles import StaticFiles
from sse_starlette.sse import EventSourceResponse

# 导入业务逻辑
from dating_agent import (
    explain_request, think_plan, 
    search_purpose_location_info, get_detailed_plan,
    RequestParams
)

from performance_optimizer import PerformanceOptimizer

app = FastAPI(title="约会规划服务 - 后端优化版", version="4.0.0")

class BackendOptimizedLimiter:
    def __init__(self):
        self.user_requests = {}  # 用户请求历史
        self.backend_requests = {}  # 后端请求历史
        
    def get_server_load(self):
        """获取服务器负载"""
        cpu_percent = psutil.cpu_percent(interval=0.1)
        memory_percent = psutil.virtual_memory().percent
        return {"cpu": cpu_percent, "memory": memory_percent}
    
    def get_user_identifier(self, request: Request) -> tuple[str, str]:
        """获取用户标识符和类型"""
        # 方法1: 从请求头获取用户ID（推荐）
        user_id = request.headers.get("X-User-ID")
        if user_id:
            return f"user:{user_id}", "authenticated_user"
        
        # 方法2: 从请求头获取会话ID
        session_id = request.headers.get("X-Session-ID")
        if session_id:
            return f"session:{session_id}", "session_user"
        
        # 方法3: 从请求头获取真实IP
        real_ip = request.headers.get("X-Real-IP") or request.headers.get("X-Forwarded-For")
        if real_ip:
            real_ip = real_ip.split(',')[0].strip()
            return f"ip:{real_ip}", "ip_user"
        
        # 方法4: 标记为后端请求
        backend_ip = request.client.host
        return f"backend:{backend_ip}", "backend_proxy"
    
    def calculate_limit(self, endpoint: str, user_type: str) -> int:
        """根据用户类型和服务器负载计算限制"""
        load = self.get_server_load()
        
        # 基础限制（每分钟）
        base_limits = {
            "explain-request": 200,
            "think-plan": 100,
            "search-location-info": 150,
            "get-detailed-plan": 80
        }
        
        base_limit = base_limits.get(endpoint, 100)
        
        # 根据用户类型调整
        type_multipliers = {
            "authenticated_user": 1.0,    # 认证用户：正常限制
            "session_user": 1.0,          # 会话用户：正常限制
            "ip_user": 0.8,               # IP用户：稍微严格
            "backend_proxy": 20.0         # 后端代理：大幅放宽（代表多个用户）
        }
        
        multiplier = type_multipliers.get(user_type, 0.5)
        adjusted_limit = int(base_limit * multiplier)
        
        # 根据服务器负载进一步调整
        if load["cpu"] < 50 and load["memory"] < 70:
            load_multiplier = 2.0  # 低负载：放宽
        elif load["cpu"] < 80 and load["memory"] < 85:
            load_multiplier = 1.0  # 中负载：正常
        else:
            load_multiplier = 0.3  # 高负载：严格保护
        
        return int(adjusted_limit * load_multiplier)
    
    def should_allow_request(self, request: Request, endpoint: str) -> tuple[bool, str]:
        """判断是否允许请求"""
        user_id, user_type = self.get_user_identifier(request)
        now = time.time()
        
        # 初始化用户请求历史
        if user_id not in self.user_requests:
            self.user_requests[user_id] = []
        
        # 清理60秒前的请求记录
        self.user_requests[user_id] = [
            req_time for req_time in self.user_requests[user_id]
            if now - req_time < 60
        ]
        
        # 获取限制
        limit = self.calculate_limit(endpoint, user_type)
        current_requests = len(self.user_requests[user_id])
        
        if current_requests >= limit:
            load = self.get_server_load()
            return False, f"请求频率过高，请稍后再试。用户类型: {user_type}, 限制: {limit}/分钟 (CPU: {load['cpu']:.1f}%, 内存: {load['memory']:.1f}%)"
        
        # 记录请求
        self.user_requests[user_id].append(now)
        return True, ""

# 初始化限流器和优化器
limiter = BackendOptimizedLimiter()
optimizer = PerformanceOptimizer()

def monitor_performance(func):
    async def wrapper(*args, **kwargs):
        start_time = time.time()
        try:
            result = await func(*args, **kwargs)
            duration = time.time() - start_time
            print(f"✅ {func.__name__} 执行成功，耗时: {duration:.2f}s")
            return result
        except Exception as e:
            duration = time.time() - start_time
            print(f"❌ {func.__name__} 执行失败，耗时: {duration:.2f}s，错误: {str(e)}")
            raise
    return wrapper

# 静态文件和模板
app.mount("/static", StaticFiles(directory="static"), name="static")

@app.get("/", response_class=HTMLResponse)
async def read_root():
    with open("templates/index.html", "r", encoding="utf-8") as f:
        return HTMLResponse(content=f.read())

@app.get("/health")
async def health_check():
    load = limiter.get_server_load()
    return {
        "status": "healthy", 
        "timestamp": time.time(),
        "server_load": load,
        "rate_limiting": "backend_optimized",
        "active_users": len(limiter.user_requests)
    }

@app.get("/metrics")
async def get_metrics():
    load = limiter.get_server_load()
    
    # 统计不同类型的用户
    user_types = {}
    for user_id in limiter.user_requests.keys():
        if user_id.startswith("user:"):
            user_types["authenticated"] = user_types.get("authenticated", 0) + 1
        elif user_id.startswith("session:"):
            user_types["session"] = user_types.get("session", 0) + 1
        elif user_id.startswith("ip:"):
            user_types["ip"] = user_types.get("ip", 0) + 1
        elif user_id.startswith("backend:"):
            user_types["backend"] = user_types.get("backend", 0) + 1
    
    return {
        "performance": {
            "cache_size": len(optimizer.cache),
            "concurrent_limit": optimizer.max_concurrent,
            "server_load": load,
            "user_statistics": user_types,
            "total_active_users": len(limiter.user_requests),
            "rate_limiting": "backend_optimized"
        },
        "timestamp": time.time()
    }

async def rate_limit_check(request: Request, endpoint: str):
    """限流检查"""
    allowed, message = limiter.should_allow_request(request, endpoint)
    if not allowed:
        yield {
            "event": "error",
            "data": json.dumps({"error": message})
        }
        return False
    return True

@app.post("/explain-request")
@monitor_performance
async def explain_request_endpoint(
    params: RequestParams, 
    request: Request,
    x_user_id: str = Header(None, alias="X-User-ID"),
    x_session_id: str = Header(None, alias="X-Session-ID"),
    x_real_ip: str = Header(None, alias="X-Real-IP")
):
    """约会期望解释 - 后端优化版"""
    
    async def generate():
        try:
            # 限流检查
            async for result in rate_limit_check(request, "explain-request"):
                if isinstance(result, dict):
                    yield result
                    return
            
            yield {
                "event": "info",
                "data": json.dumps({"content": "正在分析约会期望...\n", "type": "explain_request"})
            }

            await asyncio.sleep(0.1)
            params_dict = params.model_dump()

            # 使用缓存
            cache_key = f"explain:{hash(str(params_dict))}"
            cached_result = await optimizer._get_cache(cache_key)

            if cached_result:
                result = cached_result
            else:
                result = explain_request(params_dict)
                await optimizer._set_cache(cache_key, result, 1800)

            yield {
                "event": "message",
                "data": json.dumps({"content": result, "type": "explain_request"})
            }

            yield {
                "event": "success",
                "data": json.dumps({"message": "约会期望解释完成"})
            }
        except Exception as e:
            yield {
                "event": "error",
                "data": json.dumps({"error": str(e)})
            }

    return EventSourceResponse(generate())

if __name__ == "__main__":
    print("🚀 启动后端优化版约会规划服务器...")
    print("✨ 特性：专为Java后端调用优化，支持用户标识传递")
    print("📋 支持的请求头：")
    print("   • X-User-ID: 用户唯一标识（推荐）")
    print("   • X-Session-ID: 会话标识")
    print("   • X-Real-IP: 真实客户端IP")
    print("访问地址: http://localhost:8000")
    print("性能监控: http://localhost:8000/metrics")

    uvicorn.run(
        "backend_optimized_server:app",
        host="0.0.0.0",
        port=8000,
        log_level="info",
        access_log=True,
        reload=False
    )
