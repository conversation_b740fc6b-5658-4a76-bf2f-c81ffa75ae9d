#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
优化后的 FastAPI 服务器 - 约会规划服务
包含连接池、缓存、限流、监控等优化功能
"""

import asyncio
import json
import time
from contextlib import asynccontextmanager
from fastapi import FastAPI, Request
from fastapi.responses import HTMLResponse
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.gzip import GZipMiddleware
from pydantic import BaseModel
from sse_starlette.sse import EventSourceResponse
import uvicorn

# 导入优化模块
from performance_optimization import (
    optimizer,
    monitor_performance,
    startup_event,
    shutdown_event
)

# 导入原有的功能模块
from main import (
    explain_request,
    think_plan,
    search_purpose_location_info,
    get_detailed_plan
)

# 生命周期管理
@asynccontextmanager
async def lifespan(app: FastAPI):
    # 启动时执行
    await startup_event()
    yield
    # 关闭时执行
    await shutdown_event()

# 创建应用实例
app = FastAPI(
    title="约会规划服务 - 优化版",
    description="高性能约会规划 API，支持高并发和缓存",
    version="2.0.0",
    lifespan=lifespan
)

# 添加中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

app.add_middleware(GZipMiddleware, minimum_size=1000)

# 挂载静态文件
app.mount("/static", StaticFiles(directory="static"), name="static")

# 模板配置
templates = Jinja2Templates(directory="templates")

# 数据模型（与原版相同）
class DatingParams(BaseModel):
    to_location_province: str = "广东省"
    to_location_city: str = "深圳市"
    to_location_district: str = "福田区"
    to_location_name: str = "COCO Park"

    user_lat_lng: str = "113.999706,22.588863"
    user_location_province: str = "广东省"
    user_location_city: str = "深圳市"
    user_location_district: str = "南山区"
    user_location_name: str = "塘朗城广场"

    departure_lat_lng: str = "113.999706,22.588863"
    departure_province: str = "广东省"
    departure_city: str = "深圳市"
    departure_district: str = "南山区"
    departure_name: str = "塘朗城广场"

    comp_lat_lng: str = "114.054007,22.533569"
    comp_location_province: str = "广东省"
    comp_location_city: str = "深圳市"
    comp_location_district: str = "龙岗区"
    comp_location_name: str = "怡乐花园"

    male_last_name: str = "张"
    female_last_name: str = "李"

    trans_tool_user: str = "bus"
    trans_tool_comp: str = "car"

    date: str = "2025-05-28"
    time_start: str = "09:00"
    time_end: str = "20:00"

    dating_times: int = 3
    dating_type: str = "浪漫型"
    more_thoughts: str = "我希望约会时可以吃到火锅"

class RequestParams(BaseModel):
    user_input: str = "怎样避免约会的时候双方陷入沉默?"
    request_type: str = "dating"
    dating_params: DatingParams

# 性能监控中间件
@app.middleware("http")
async def performance_middleware(request: Request, call_next):
    start_time = time.time()
    response = await call_next(request)
    process_time = time.time() - start_time
    response.headers["X-Process-Time"] = str(process_time)
    print(f"[PERF] {request.method} {request.url.path} - {process_time:.2f}s")
    return response

# 生命周期事件已在上面的 lifespan 函数中处理

# 路由定义
@app.get("/", response_class=HTMLResponse)
async def read_root(request: Request):
    """返回前端页面"""
    return templates.TemplateResponse("index.html", {"request": request})

@app.get("/health")
async def health_check():
    """健康检查端点"""
    return {
        "status": "healthy",
        "timestamp": time.time(),
        "version": "2.0.0"
    }

@app.get("/metrics")
async def get_metrics():
    """性能指标端点"""
    return {
        "active_connections": len(optimizer.rate_limiter),
        "cache_status": "memory",
        "cache_entries": len(optimizer.memory_cache),
        "semaphore_available": optimizer.request_semaphore._value,
        "timestamp": time.time()
    }

@app.post("/explain-request")
@monitor_performance
async def explain_request_endpoint(params: RequestParams, request: Request):
    """解释用户约会期望 - 优化版"""
    client_id = request.client.host

    @optimizer.rate_limit(max_requests=20, window=60)
    async def generate(client_id: str = client_id):
        try:
            yield {
                "event": "info",
                "data": json.dumps({"content": "正在分析约会期望...\n", "type": "explain_request"})
            }

            await asyncio.sleep(0.1)
            params_dict = params.model_dump()

            # 使用缓存
            cache_key = f"explain:{hash(str(params_dict))}"

            @optimizer.cached_request(cache_key, cache_ttl=1800)  # 30分钟缓存
            async def get_explanation():
                return explain_request(params_dict)

            result = await get_explanation()

            yield {
                "event": "message",
                "data": json.dumps({"content": result, "type": "explain_request"})
            }

            yield {
                "event": "success",
                "data": json.dumps({"message": "约会期望解释完成"})
            }
        except Exception as e:
            yield {
                "event": "error",
                "data": json.dumps({"error": str(e)})
            }

    return EventSourceResponse(generate())

@app.post("/think-plan")
@monitor_performance
async def think_plan_endpoint(params: RequestParams, request: Request):
    """思考规划 - 优化版"""
    client_id = request.client.host

    @optimizer.rate_limit(max_requests=10, window=60)
    async def generate(client_id: str = client_id):
        try:
            params_dict = params.model_dump()
            async with optimizer.concurrent_limit():
                async for content in think_plan(params_dict):
                    yield {
                        "event": "message",
                        "data": json.dumps({"content": content, "type": "think_plan"})
                    }
            yield {
                "event": "success",
                "data": json.dumps({"message": "思考规划完成"})
            }
        except Exception as e:
            yield {
                "event": "error",
                "data": json.dumps({"error": str(e)})
            }

    return EventSourceResponse(generate())

@app.post("/search-location-info")
@monitor_performance
async def search_location_info_endpoint(params: RequestParams, request: Request):
    """查询目的地信息 - 优化版"""
    client_id = request.client.host

    @optimizer.rate_limit(max_requests=15, window=60)
    async def generate(client_id: str = client_id):
        try:
            yield {
                "event": "info",
                "data": json.dumps({"content": "开始查询目的地信息...\n", "type": "search_location_info"})
            }

            params_dict = params.model_dump()

            # 使用缓存
            location_key = f"{params_dict['dating_params']['to_location_province']}-{params_dict['dating_params']['to_location_city']}-{params_dict['dating_params']['to_location_name']}"
            cache_key = f"location:{hash(location_key)}"

            @optimizer.cached_request(cache_key, cache_ttl=3600)  # 1小时缓存
            async def get_location_info():
                return await search_purpose_location_info(params_dict)

            async with optimizer.concurrent_limit():
                result = await get_location_info()
                final_result = {"success": True, "data": result}

            yield {
                "event": "message",
                "data": json.dumps({"content": json.dumps(final_result, ensure_ascii=False, indent=2), "type": "search_location_info"})
            }

            yield {
                "event": "success",
                "data": json.dumps({"message": "目的地信息查询完成"})
            }

        except Exception as e:
            # 错误处理逻辑（与原版相同）
            yield {
                "event": "error",
                "data": json.dumps({"error": str(e)})
            }

    return EventSourceResponse(generate())

@app.post("/get-detailed-plan")
@monitor_performance
async def get_detailed_plan_endpoint(params: RequestParams, request: Request):
    """获取详细计划 - 优化版"""
    client_id = request.client.host

    @optimizer.rate_limit(max_requests=5, window=60)
    async def generate(client_id: str = client_id):
        try:
            params_dict = params.model_dump()
            async with optimizer.concurrent_limit():
                async for content in get_detailed_plan(params_dict):
                    yield {
                        "event": "message",
                        "data": json.dumps({"content": content, "type": "detailed_plan"})
                    }
            yield {
                "event": "success",
                "data": json.dumps({"message": "详细规划完成"})
            }
        except Exception as e:
            yield {
                "event": "error",
                "data": json.dumps({"error": str(e)})
            }

    return EventSourceResponse(generate())

# 生产环境配置
if __name__ == "__main__":
    print("启动优化版约会规划服务器...")
    print("访问地址: http://localhost:8000")
    print("性能监控: http://localhost:8000/metrics")

    try:
        # 尝试使用高性能配置
        uvicorn.run(
            "optimized_server:app",
            host="0.0.0.0",
            port=8000,
            log_level="info",
            access_log=True,
            reload=False
        )
    except Exception as e:
        print(f"启动失败: {e}")
        # 降级到基本配置
        uvicorn.run(
            app,
            host="0.0.0.0",
            port=8000,
            log_level="info"
        )
