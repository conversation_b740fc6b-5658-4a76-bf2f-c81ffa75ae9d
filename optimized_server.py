#!/usr/bin/env python
# -*- coding: utf-8 -*-
'''
@File	:optimized_server.py
@Time	:2025/05/28 11:07:58
<AUTHOR>
@Mail	:<EMAIL>
'''

import asyncio
import json
import time
import psutil
from contextlib import asynccontextmanager
from fastapi import FastAPI, Request
from fastapi.responses import HTMLResponse
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.gzip import GZipMiddleware
from pydantic import BaseModel
from sse_starlette.sse import EventSourceResponse
import uvicorn
from logger import LOGGER

# 导入优化模块
from performance_optimization import (
    optimizer,
    monitor_performance,
    startup_event,
    shutdown_event
)

# 导入原有的功能模块
from main import (
    explain_request,
    think_plan,
    search_purpose_location_info,
    get_detailed_plan
)

# 请求间隔控制器
class RequestIntervalController:
    def __init__(self, min_interval: float = 0.5, max_interval: float = 1.5):
        """
        初始化请求间隔控制器
        Args:
            min_interval: 最小间隔时间（秒）
            max_interval: 最大间隔时间（秒）
        """
        self.min_interval = min_interval
        self.max_interval = max_interval
        self.last_request_time = {}  # 记录每个端点的最后请求时间
        self.request_lock = asyncio.Lock()

    async def wait_for_interval(self, endpoint: str):
        """为指定端点等待适当的间隔时间（智能控制）"""
        async with self.request_lock:
            current_time = time.time()
            last_time = self.last_request_time.get(endpoint, 0)

            # 计算需要等待的时间
            elapsed = current_time - last_time

            # 智能间隔控制：只有在间隔太短时才等待
            if elapsed < self.min_interval:
                wait_time = self.min_interval - elapsed
                LOGGER.info(f"[间隔控制] {endpoint} 等待 {wait_time:.2f}s 以避免API压力")
                await asyncio.sleep(wait_time)

            # 更新最后请求时间
            self.last_request_time[endpoint] = time.time()

# CPU状态限流器
class CPUBasedLimiter:
    def __init__(self):
        self.request_count = 0
        self.last_check_time = time.time()

    def get_cpu_status(self):
        """获取CPU使用率"""
        return psutil.cpu_percent(interval=0.1)

    def should_allow_request(self, endpoint: str) -> tuple[bool, str]:
        """基于CPU状态判断是否允许请求"""
        cpu_usage = self.get_cpu_status()

        # 非常宽松的CPU限流策略
        if cpu_usage > 95:
            return False, f"服务器负载过高 (CPU: {cpu_usage:.1f}%)，请稍后再试"
        elif cpu_usage > 90:
            # 高负载时稍微限制一下
            self.request_count += 1
            if self.request_count % 10 == 0:  # 每10个请求检查一次
                return False, f"服务器负载较高 (CPU: {cpu_usage:.1f}%)，请稍后再试"

        # CPU使用率在90%以下时，基本不限流
        return True, ""

# 初始化控制器
interval_controller = RequestIntervalController(
    min_interval=0.2,  # 最小间隔0.2秒（只有在请求间隔小于0.2秒时才等待）
    max_interval=0.5   # 保留参数兼容性
)
cpu_limiter = CPUBasedLimiter()

# 生命周期管理
@asynccontextmanager
async def lifespan(app: FastAPI):
    # 启动时执行
    await startup_event()
    yield
    # 关闭时执行
    await shutdown_event()

# 创建应用实例
app = FastAPI(
    title="约会规划服务 - 优化版",
    description="高性能约会规划 API，支持高并发和缓存",
    version="2.0.0",
    lifespan=lifespan
)

# 添加中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

app.add_middleware(GZipMiddleware, minimum_size=1000)

# 挂载静态文件
app.mount("/static", StaticFiles(directory="static"), name="static")

# 模板配置
templates = Jinja2Templates(directory="templates")

# 数据模型（与原版相同）
class DatingParams(BaseModel):
    to_location_province: str = "广东省"
    to_location_city: str = "深圳市"
    to_location_district: str = "福田区"
    to_location_name: str = "COCO Park"

    user_lat_lng: str = "113.999706,22.588863"
    user_location_province: str = "广东省"
    user_location_city: str = "深圳市"
    user_location_district: str = "南山区"
    user_location_name: str = "塘朗城广场"

    departure_lat_lng: str = "113.999706,22.588863"
    departure_province: str = "广东省"
    departure_city: str = "深圳市"
    departure_district: str = "南山区"
    departure_name: str = "塘朗城广场"

    comp_lat_lng: str = "114.054007,22.533569"
    comp_location_province: str = "广东省"
    comp_location_city: str = "深圳市"
    comp_location_district: str = "龙岗区"
    comp_location_name: str = "怡乐花园"

    male_last_name: str = "张"
    female_last_name: str = "李"

    trans_tool_user: str = "bus"
    trans_tool_comp: str = "car"

    date: str = "2025-05-28"
    time_start: str = "09:00"
    time_end: str = "20:00"

    dating_times: int = 3
    dating_type: str = "浪漫型"
    more_thoughts: str = "我希望约会时可以吃到火锅"

class RequestParams(BaseModel):
    user_input: str = "怎样避免约会的时候双方陷入沉默?"
    request_type: str = "dating"
    dating_params: DatingParams

# 性能监控中间件
@app.middleware("http")
async def performance_middleware(request: Request, call_next):
    start_time = time.time()
    response = await call_next(request)
    process_time = time.time() - start_time
    response.headers["X-Process-Time"] = str(process_time)
    LOGGER.info(f"[PERF] {request.method} {request.url.path} - {process_time:.2f}s")
    return response

# 生命周期事件已在上面的 lifespan 函数中处理

# 路由定义
@app.get("/", response_class=HTMLResponse)
async def read_root(request: Request):
    """返回前端页面"""
    return templates.TemplateResponse("index.html", {"request": request})

@app.get("/health")
async def health_check():
    """健康检查端点"""
    return {
        "status": "healthy",
        "timestamp": time.time(),
        "version": "2.0.0"
    }

@app.get("/metrics")
async def get_metrics():
    """性能指标端点"""
    cpu_usage = cpu_limiter.get_cpu_status()
    memory_info = psutil.virtual_memory()

    return {
        "server_load": {
            "cpu_percent": cpu_usage,
            "memory_percent": memory_info.percent,
            "memory_available_gb": round(memory_info.available / (1024**3), 2)
        },
        "rate_limiting": {
            "type": "cpu_based",
            "cpu_threshold_high": 95,
            "cpu_threshold_medium": 90,
            "current_status": "normal" if cpu_usage < 90 else "limited" if cpu_usage < 95 else "restricted"
        },
        "interval_control": {
            "enabled": True,
            "min_interval": interval_controller.min_interval,
            "max_interval": interval_controller.max_interval,
            "tracked_endpoints": list(interval_controller.last_request_time.keys()),
            "last_request_times": {
                endpoint: round(time.time() - last_time, 2)
                for endpoint, last_time in interval_controller.last_request_time.items()
            }
        },
        "cache_status": "memory",
        "cache_entries": len(optimizer.memory_cache),
        "semaphore_available": optimizer.request_semaphore._value,
        "request_count": cpu_limiter.request_count,
        "timestamp": time.time()
    }

@app.post("/explain-request")
@monitor_performance
async def explain_request_endpoint(params: RequestParams, request: Request):
    """解释用户约会期望 - 优化版"""

    async def generate():
        try:
            # CPU状态限流检查
            allowed, message = cpu_limiter.should_allow_request("explain-request")
            if not allowed:
                yield {
                    "event": "error",
                    "data": json.dumps({"error": message})
                }
                LOGGER.warning(f"[限流] 拒绝请求: {message}")
                return

            yield {
                "event": "info",
                "data": json.dumps({"content": "正在分析约会期望...\n", "type": "explain_request"})
            }

            await asyncio.sleep(0.1)
            params_dict = params.model_dump()

            # 使用缓存
            cache_key = f"explain:{hash(str(params_dict))}"
            cached_result = await optimizer._get_cache(cache_key)

            if cached_result:
                result = cached_result
            else:
                result = explain_request(params_dict)
                await optimizer._set_cache(cache_key, result, 1800)  # 30分钟缓存

            yield {
                "event": "message",
                "data": json.dumps({"content": result, "type": "explain_request"})
            }
            LOGGER.info(f'约会期望解释结果: {result}')

            yield {
                "event": "success",
                "data": json.dumps({"message": "约会期望解释完成"})
            }
            LOGGER.info('约会期望解释完成')
        except Exception as e:
            yield {
                "event": "error",
                "data": json.dumps({"error": str(e)})
            }
            import traceback
            LOGGER.error(f"Error in explain_request_endpoint: {str(e)}")
            LOGGER.error(f"Traceback: {traceback.format_exc()}")

    return EventSourceResponse(generate())

@app.post("/think-plan")
@monitor_performance
async def think_plan_endpoint(params: RequestParams, request: Request):
    """思考规划 - 优化版"""

    async def generate():
        try:
            # CPU状态限流检查
            allowed, message = cpu_limiter.should_allow_request("think-plan")
            if not allowed:
                yield {
                    "event": "error",
                    "data": json.dumps({"error": message})
                }
                LOGGER.warning(f"[限流] 拒绝请求: {message}")
                return

            params_dict = params.model_dump()
            async with optimizer.concurrent_limit():
                async for content in think_plan(params_dict):
                    yield {
                        "event": "message",
                        "data": json.dumps({"content": content, "type": "think_plan"})
                    }
                    # LOGGER.info(f'思考规划内容: {content}')
            yield {
                "event": "success",
                "data": json.dumps({"message": "思考规划完成"})
            }
            LOGGER.info('思考规划完成')
        except Exception as e:
            yield {
                "event": "error",
                "data": json.dumps({"error": str(e)})
            }
            import traceback
            LOGGER.error(f"Error in think_plan_endpoint: {str(e)}")
            LOGGER.error(f"Traceback: {traceback.format_exc()}")

    return EventSourceResponse(generate())

@app.post("/search-location-info")
@monitor_performance
async def search_location_info_endpoint(params: RequestParams, request: Request):
    """查询目的地信息 - 优化版"""

    async def generate():
        try:
            # 请求间隔控制 - 在处理请求前等待
            await interval_controller.wait_for_interval("search-location-info")

            # CPU状态限流检查
            allowed, message = cpu_limiter.should_allow_request("search-location-info")
            if not allowed:
                yield {
                    "event": "error",
                    "data": json.dumps({"error": message})
                }
                LOGGER.warning(f"[限流] 拒绝请求: {message}")
                return

            yield {
                "event": "info",
                "data": json.dumps({"content": "开始查询目的地信息...\n", "type": "search_location_info"})
            }

            params_dict = params.model_dump()

            # 使用缓存
            location_key = f"{params_dict['dating_params']['to_location_province']}-{params_dict['dating_params']['to_location_city']}-{params_dict['dating_params']['to_location_name']}"
            cache_key = f"location:{hash(location_key)}"

            cached_result = await optimizer._get_cache(cache_key)

            if cached_result:
                result = cached_result
            else:
                async with optimizer.concurrent_limit():
                    result = await search_purpose_location_info(params_dict)
                    await optimizer._set_cache(cache_key, result, 3600)  # 1小时缓存

            final_result = {"success": True, "data": result}

            yield {
                "event": "message",
                "data": json.dumps({"content": json.dumps(final_result, ensure_ascii=False, indent=2), "type": "search_location_info"})
            }
            LOGGER.info('目的地信息查询结果已返回')

            yield {
                "event": "success",
                "data": json.dumps({"message": "目的地信息查询完成"})
            }
            LOGGER.info('目的地信息查询完成')

        except Exception as e:
            # 错误处理逻辑（与原版相同）
            yield {
                "event": "error",
                "data": json.dumps({"error": str(e)})
            }
            import traceback
            LOGGER.error(f"Error in search_location_info_endpoint: {str(e)}")
            LOGGER.error(f"Traceback: {traceback.format_exc()}")

    return EventSourceResponse(generate())

@app.post("/get-detailed-plan")
@monitor_performance
async def get_detailed_plan_endpoint(params: RequestParams, request: Request):
    """获取详细计划 - 优化版"""

    async def generate():
        try:
            # 请求间隔控制 - 在处理请求前等待（仅对约会规划类型）
            params_dict = params.model_dump()
            if params_dict.get('request_type') == 'dating':
                await interval_controller.wait_for_interval("get-detailed-plan")

            # CPU状态限流检查
            allowed, message = cpu_limiter.should_allow_request("get-detailed-plan")
            if not allowed:
                yield {
                    "event": "error",
                    "data": json.dumps({"error": message})
                }
                LOGGER.warning(f"[限流] 拒绝请求: {message}")
                return
            async with optimizer.concurrent_limit():
                async for content in get_detailed_plan(params_dict):
                    yield {
                        "event": "message",
                        "data": json.dumps({"content": content, "type": "detailed_plan"})
                    }
                    # LOGGER.info({"event": "message", "data": json.dumps({"content": content, "type": "detailed_plan"},ensure_ascii=False)})
            yield {
                "event": "success",
                "data": json.dumps({"message": "详细规划完成"})
            }
            LOGGER.info('详细规划完成')
        except Exception as e:
            import traceback
            error_msg = str(e)
            error_traceback = traceback.format_exc()
            LOGGER.error(f"get_detailed_plan_endpoint 异常: {error_msg}")
            LOGGER.error(f"异常堆栈: {error_traceback}")
            yield {
                "event": "error",
                "data": json.dumps({"error": error_msg, "traceback": error_traceback})
            }

    return EventSourceResponse(generate())

# 生产环境配置
if __name__ == "__main__":
    LOGGER.info("启动约会规划服务器...")
    LOGGER.info("限流策略：")
    LOGGER.info("   • CPU < 90%: 无限制")
    LOGGER.info("   • CPU 90-95%: 轻微限制（每10个请求检查一次）")
    LOGGER.info("   • CPU > 95%: 暂时拒绝请求")
    LOGGER.info("请求间隔控制：")
    LOGGER.info(f"   • 最小间隔: {interval_controller.min_interval}秒")
    LOGGER.info(f"   • 最大间隔: {interval_controller.max_interval}秒")
    LOGGER.info("   • 目的: 减轻高德地图API并发压力")
    LOGGER.info("🌐 访问地址: http://localhost:8000")
    LOGGER.info("📈 性能监控: http://localhost:8000/metrics")

    try:
        # 尝试使用高性能配置
        uvicorn.run(
            "optimized_server:app",
            host="0.0.0.0",
            port=8000,
            log_level="info",
            access_log=True,
            reload=False
        )
    except Exception as e:
        print(f"启动失败: {e}")
        # 降级到基本配置
        uvicorn.run(
            app,
            host="0.0.0.0",
            port=8000,
            log_level="info"
        )
