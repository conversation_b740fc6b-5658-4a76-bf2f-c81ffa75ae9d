#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
性能测试脚本 - 约会规划服务
测试并发能力和响应时间
"""

import asyncio
import aiohttp
import time
import json
from typing import List, Dict
import statistics

class PerformanceTester:
    """性能测试器"""

    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.test_data = {
            "user_input": "怎样避免约会的时候双方陷入沉默?",
            "request_type": "dating",
            "dating_params": {
                "to_location_province": "广东省",
                "to_location_city": "深圳市",
                "to_location_district": "福田区",
                "to_location_name": "COCO Park",
                "user_lat_lng": "113.999706,22.588863",
                "user_location_province": "广东省",
                "user_location_city": "深圳市",
                "user_location_district": "南山区",
                "user_location_name": "塘朗城广场",
                "departure_lat_lng": "113.999706,22.588863",
                "departure_province": "广东省",
                "departure_city": "深圳市",
                "departure_district": "南山区",
                "departure_name": "塘朗城广场",
                "comp_lat_lng": "114.054007,22.533569",
                "comp_location_province": "广东省",
                "comp_location_city": "深圳市",
                "comp_location_district": "龙岗区",
                "comp_location_name": "怡乐花园",
                "male_last_name": "张",
                "female_last_name": "李",
                "trans_tool_user": "bus",
                "trans_tool_comp": "car",
                "date": "2025-05-28",
                "time_start": "09:00",
                "time_end": "20:00",
                "dating_times": 3,
                "dating_type": "浪漫型",
                "more_thoughts": "我希望约会时可以吃到火锅"
            }
        }

    async def single_request(self, session: aiohttp.ClientSession, endpoint: str) -> Dict:
        """单个请求测试"""
        start_time = time.time()
        try:
            async with session.post(
                f"{self.base_url}/{endpoint}",
                json=self.test_data,
                headers={"Content-Type": "application/json"}
            ) as response:
                if endpoint in ["think-plan", "search-location-info", "get-detailed-plan"]:
                    # SSE 端点
                    content = ""
                    async for line in response.content:
                        if line:
                            content += line.decode()
                            print('================')
                            print(content)
                            print('================')

                    duration = time.time() - start_time
                    return {
                        "status": response.status,
                        "duration": duration,
                        "content_length": len(content),
                        "success": response.status == 200
                    }
                else:
                    # 普通端点
                    content = await response.text()
                    duration = time.time() - start_time
                    return {
                        "status": response.status,
                        "duration": duration,
                        "content_length": len(content),
                        "success": response.status == 200
                    }
        except Exception as e:
            duration = time.time() - start_time
            return {
                "status": 0,
                "duration": duration,
                "content_length": 0,
                "success": False,
                "error": str(e)
            }

    async def concurrent_test(self, endpoint: str, concurrent_users: int, total_requests: int) -> Dict:
        """并发测试"""
        print(f"\n🚀 开始测试端点: {endpoint}")
        print(f"并发用户数: {concurrent_users}, 总请求数: {total_requests}")

        connector = aiohttp.TCPConnector(limit=concurrent_users * 2)
        timeout = aiohttp.ClientTimeout(total=60)

        async with aiohttp.ClientSession(connector=connector, timeout=timeout) as session:
            # 创建信号量限制并发
            semaphore = asyncio.Semaphore(concurrent_users)

            async def limited_request():
                async with semaphore:
                    return await self.single_request(session, endpoint)

            # 执行测试
            start_time = time.time()
            tasks = [limited_request() for _ in range(total_requests)]
            results = await asyncio.gather(*tasks, return_exceptions=True)
            total_duration = time.time() - start_time

            # 统计结果
            successful_results = [r for r in results if isinstance(r, dict) and r.get("success")]
            failed_results = [r for r in results if not (isinstance(r, dict) and r.get("success"))]

            if successful_results:
                durations = [r["duration"] for r in successful_results]
                response_times = {
                    "min": min(durations),
                    "max": max(durations),
                    "avg": statistics.mean(durations),
                    "median": statistics.median(durations),
                    "p95": statistics.quantiles(durations, n=20)[18] if len(durations) > 20 else max(durations)
                }
            else:
                response_times = {"min": 0, "max": 0, "avg": 0, "median": 0, "p95": 0}

            return {
                "endpoint": endpoint,
                "concurrent_users": concurrent_users,
                "total_requests": total_requests,
                "successful_requests": len(successful_results),
                "failed_requests": len(failed_results),
                "success_rate": len(successful_results) / total_requests * 100,
                "total_duration": total_duration,
                "requests_per_second": total_requests / total_duration,
                "response_times": response_times,
                "errors": [str(r) for r in failed_results if isinstance(r, Exception)][:5]  # 只显示前5个错误
            }

    async def health_check(self) -> bool:
        """健康检查"""
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(f"{self.base_url}/health") as response:
                    return response.status == 200
        except:
            return False

    async def run_comprehensive_test(self):
        """运行综合性能测试"""
        print("=" * 60)
        print("🎯 约会规划服务性能测试")
        print("=" * 60)

        # 健康检查
        if not await self.health_check():
            print("❌ 服务不可用，请先启动服务器")
            return

        print("✅ 服务健康检查通过")

        # 测试配置 - 包含所有端点
        test_configs = [
            {"endpoint": "explain-request", "concurrent": 10, "total": 50},
            {"endpoint": "explain-request", "concurrent": 20, "total": 100},
            {"endpoint": "think-plan", "concurrent": 5, "total": 25},
            {"endpoint": "think-plan", "concurrent": 10, "total": 50},
            {"endpoint": "search-location-info", "concurrent": 5, "total": 25},
            {"endpoint": "search-location-info", "concurrent": 10, "total": 50},
            {"endpoint": "get-detailed-plan", "concurrent": 3, "total": 15},
            {"endpoint": "get-detailed-plan", "concurrent": 5, "total": 25},
        ]

        results = []

        for config in test_configs:
            try:
                result = await self.concurrent_test(
                    config["endpoint"],
                    config["concurrent"],
                    config["total"]
                )
                results.append(result)

                # 打印结果
                print(f"\n📊 测试结果:")
                print(f"端点: {result['endpoint']}")
                print(f"成功率: {result['success_rate']:.1f}%")
                print(f"QPS: {result['requests_per_second']:.1f}")
                print(f"响应时间 - 平均: {result['response_times']['avg']:.2f}s, P95: {result['response_times']['p95']:.2f}s")

                if result['errors']:
                    print(f"错误示例: {result['errors'][0]}")

                # 测试间隔
                await asyncio.sleep(2)

            except Exception as e:
                print(f"❌ 测试失败: {str(e)}")

        # 生成报告
        self.generate_report(results)

    def generate_report(self, results: List[Dict]):
        """生成详细的性能测试报告"""
        print("\n" + "=" * 80)
        print("📈 约会规划服务 - 完整性能测试报告")
        print("=" * 80)

        # 按端点分组
        endpoints = {}
        for result in results:
            endpoint = result['endpoint']
            if endpoint not in endpoints:
                endpoints[endpoint] = []
            endpoints[endpoint].append(result)

        # 详细报告
        for endpoint, endpoint_results in endpoints.items():
            print(f"\n🎯 {endpoint.upper()} 端点性能分析")
            print("-" * 50)

            for result in endpoint_results:
                print(f"   并发: {result['concurrent_users']:2d} | "
                      f"成功率: {result['success_rate']:5.1f}% | "
                      f"QPS: {result['requests_per_second']:6.1f} | "
                      f"平均响应: {result['response_times']['avg']:5.2f}s | "
                      f"P95响应: {result['response_times']['p95']:5.2f}s")

        # 综合分析
        print(f"\n📊 综合性能分析")
        print("=" * 50)

        max_qps = max([r['requests_per_second'] for r in results])
        avg_success_rate = sum([r['success_rate'] for r in results]) / len(results)
        avg_response_time = sum([r['response_times']['avg'] for r in results]) / len(results)
        max_concurrent = max([r['concurrent_users'] for r in results])

        print(f"最高 QPS: {max_qps:.1f} 请求/秒")
        print(f"平均成功率: {avg_success_rate:.1f}%")
        print(f"平均响应时间: {avg_response_time:.2f}秒")
        print(f"最大测试并发: {max_concurrent} 用户")

        # 并发支持能力评估
        print(f"\n🚀 并发支持能力评估")
        print("=" * 50)

        # 基于测试结果推算理论并发能力
        stable_qps = min([r['requests_per_second'] for r in results if r['success_rate'] > 95])
        theoretical_concurrent = int(stable_qps * 60)  # 每分钟处理的请求数

        print(f"稳定 QPS (成功率>95%): {stable_qps:.1f}")
        print(f"理论并发支持: {theoretical_concurrent} 用户/分钟")
        print(f"推荐并发用户数: {int(stable_qps * 10)} 用户 (10秒窗口)")

        # 性能等级评估
        if stable_qps > 500:
            level = "🏆 优秀"
        elif stable_qps > 100:
            level = "🥇 良好"
        elif stable_qps > 50:
            level = "🥈 中等"
        else:
            level = "🥉 需要优化"

        print(f"性能等级: {level}")

        # 建议
        print(f"\n💡 优化建议")
        print("=" * 50)

        if avg_success_rate > 99:
            print("✅ 服务稳定性优秀")
        elif avg_success_rate > 95:
            print("✅ 服务稳定性良好")
        else:
            print("⚠️  建议优化错误处理和超时设置")

        if avg_response_time < 0.1:
            print("✅ 响应时间优秀")
        elif avg_response_time < 0.5:
            print("✅ 响应时间良好")
        else:
            print("⚠️  建议优化响应时间")

        if stable_qps > 100:
            print("✅ 并发处理能力良好")
        else:
            print("⚠️  建议优化并发处理能力")

        # 实际部署建议
        print(f"\n🎯 实际部署建议")
        print("=" * 50)
        print(f"• 单实例支持: {int(stable_qps * 5)} 并发用户")
        print(f"• 负载均衡 (2实例): {int(stable_qps * 10)} 并发用户")
        print(f"• 负载均衡 (4实例): {int(stable_qps * 20)} 并发用户")
        print(f"• 微服务架构: {int(stable_qps * 50)}+ 并发用户")

async def main():
    """主函数"""
    tester = PerformanceTester()
    await tester.run_comprehensive_test()

if __name__ == "__main__":
    asyncio.run(main())
