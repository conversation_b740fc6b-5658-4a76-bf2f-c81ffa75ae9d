#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
演示版 FastAPI 服务器 - 约会规划服务
不依赖外部 API，用于演示 SSE 功能
"""

import asyncio
import json
import time
from typing import Dict, Any
from fastapi import FastAPI, Request, HTTPException
from fastapi.responses import HTMLResponse, StreamingResponse
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from pydantic import BaseModel
from sse_starlette.sse import EventSourceResponse

app = FastAPI(title="约会规划服务（演示版）", description="基于 SSE 的约会规划 API - 演示版")

# 挂载静态文件
app.mount("/static", StaticFiles(directory="static"), name="static")

# 模板配置
templates = Jinja2Templates(directory="templates")


# 请求模型定义
class DatingParams(BaseModel):
    to_location_province: str = "广东省"
    to_location_city: str = "深圳市"
    to_location_district: str = "福田区"
    to_location_name: str = "COCO Park"

    user_lat_lng: str = "113.999706,22.588863"
    user_location_province: str = "广东省"
    user_location_city: str = "深圳市"
    user_location_district: str = "南山区"
    user_location_name: str = "塘朗城广场"

    departure_lat_lng: str = "113.999706,22.588863"
    departure_province: str = "广东省"
    departure_city: str = "深圳市"
    departure_district: str = "南山区"
    departure_name: str = "塘朗城广场"

    comp_lat_lng: str = "114.054007,22.533569"
    comp_location_province: str = "广东省"
    comp_location_city: str = "深圳市"
    comp_location_district: str = "宝安区"
    comp_location_name: str = "尚都花园"

    male_last_name: str = "张"
    female_last_name: str = "李"

    trans_tool_user: str = "bus"  # bus-公交 car-自驾
    trans_tool_comp: str = "bus"  # bus-公交 car-自驾

    date: str = "2025-05-24"
    time_start: str = "14:00"
    time_end: str = "17:00"

    dating_times: int = 1  # 1-1次 2-2次 3-3多次 4-确认关系
    dating_type: str = "浪漫型"  # 浪漫型/趣味型/文艺型
    more_thoughts: str = "我希望约会时可以吃到冰淇淋"


class RequestParams(BaseModel):
    user_input: str = "这周六下午我想和女朋友在COCO Park约会"
    request_type: str = "dating"  # 'dating':约会规划，'chat':对话聊天
    dating_params: DatingParams


# 演示用的模拟函数
def demo_explain_request(params: dict) -> str:
    """演示版：解释用户约会期望"""
    dating_params = params['dating_params']
    trans_tool_dict = {'bus': '公交', 'car': '自驾'}

    user_departure = dating_params['departure_district']
    user_trans_tool = trans_tool_dict[dating_params['trans_tool_user']]
    comp_departure = dating_params['comp_location_district']
    comp_trans_tool = trans_tool_dict[dating_params['trans_tool_comp']]
    dating_date = dating_params['date']
    dating_time_start = dating_params['time_start']
    dating_time_end = dating_params['time_end']
    dating_area = dating_params['to_location_district'] + dating_params['to_location_name']
    dating_type = dating_params['dating_type']

    explain = f'用户出发地:{user_departure}、{user_trans_tool};伴侣出发地:{comp_departure}、{comp_trans_tool};约会时间:{dating_date} {dating_time_start}-{dating_time_end};约会区域范围为{dating_area};约会格调为{dating_type}'

    return explain


async def demo_think_plan(params: dict):
    """演示版：思考规划过程"""
    explain = demo_explain_request(params)

    thoughts = [
        f"📝 分析用户需求：{explain}\n",
        "🤔 开始分析约会场景...\n",
        "📍 确认约会地点：COCO Park 是深圳福田区的热门商业中心\n",
        "🚌 分析交通方式：双方都选择公交出行，需要规划最佳路线\n",
        "⏰ 时间安排：下午2点到5点，适合休闲约会\n",
        "💝 约会风格：浪漫型约会，需要营造温馨氛围\n",
        "🍦 特殊需求：用户希望能吃到冰淇淋\n",
        "🎯 开始制定详细计划...\n",
        "✅ 需求分析完成！\n"
    ]

    for thought in thoughts:
        await asyncio.sleep(0.5)  # 模拟思考时间
        yield thought


async def demo_search_location_info(params: dict) -> dict:
    """演示版：查询目的地信息"""
    await asyncio.sleep(1)  # 模拟查询时间

    return {
        'to_location_food': [
            {'name': '海底捞火锅', 'photos': ['photo1.jpg', 'photo2.jpg']},
            {'name': '星巴克咖啡', 'photos': ['photo3.jpg']},
            {'name': '哈根达斯', 'photos': ['photo4.jpg', 'photo5.jpg']},
        ],
        'to_location_shopping': [
            {'name': 'ZARA', 'photos': ['shop1.jpg']},
            {'name': '优衣库', 'photos': ['shop2.jpg']},
        ],
        'to_location_entertainment': [
            {'name': 'CGV影城', 'photos': ['cinema1.jpg']},
            {'name': 'KTV', 'photos': ['ktv1.jpg']},
        ],
        'to_location_sightseeing': [
            {'name': '中心公园', 'photos': ['park1.jpg']},
        ],
        'count': 7
    }


async def demo_get_detailed_plan(params: dict):
    """演示版：获取详细计划"""
    plan_parts = [
        "🌟 **浪漫约会计划** 🌟\n\n",
        "📅 **约会时间**：2025年5月24日 14:00-17:00\n",
        "📍 **约会地点**：深圳福田区 COCO Park\n\n",
        "🚌 **交通安排**：\n",
        "• 张先生从南山区塘朗城广场出发\n",
        "• 李女士从宝安区尚都花园出发\n",
        "• 建议乘坐地铁，约13:30出发\n\n",
        "⏰ **详细时间安排**：\n\n",
        "**14:00-14:30** 🤝 相聚时光\n",
        "• 在COCO Park一楼中庭汇合\n",
        "• 简单拍照留念\n\n",
        "**14:30-15:30** ☕ 下午茶时光\n",
        "• 前往星巴克享用咖啡和轻食\n",
        "• 聊天交流，增进感情\n\n",
        "**15:30-16:30** 🛍️ 浪漫购物\n",
        "• 逛逛ZARA、优衣库等品牌店\n",
        "• 为彼此挑选小礼物\n\n",
        "**16:30-17:00** 🍦 甜蜜时光\n",
        "• 到哈根达斯享用冰淇淋\n",
        "• 满足用户的特殊需求\n",
        "• 在甜蜜中结束约会\n\n",
        "💡 **贴心提示**：\n",
        "• 建议准备雨伞，以防天气变化\n",
        "• 穿着舒适的鞋子，方便逛街\n",
        "• 记得带充电宝，保持联系畅通\n\n",
        "❤️ 祝您约会愉快！\n"
    ]

    for part in plan_parts:
        await asyncio.sleep(0.3)  # 模拟生成时间
        yield part


@app.get("/", response_class=HTMLResponse)
async def read_root(request: Request):
    """返回前端页面"""
    return templates.TemplateResponse("index.html", {"request": request})


@app.post("/explain-request")
async def explain_request_endpoint(params: RequestParams):
    """解释用户约会期望"""
    try:
        params_dict = params.model_dump()
        result = demo_explain_request(params_dict)
        return {"success": True, "data": result}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/think-plan")
async def think_plan_endpoint(params: RequestParams):
    """用户需求理解和任务拆分 - SSE 流式响应"""
    async def generate():
        try:
            params_dict = params.model_dump()
            async for content in demo_think_plan(params_dict):
                yield {
                    "event": "message",
                    "data": json.dumps({"content": content, "type": "think_plan"})
                }
            yield {
                "event": "end",
                "data": json.dumps({"message": "思考规划完成"})
            }
        except Exception as e:
            yield {
                "event": "error",
                "data": json.dumps({"error": str(e)})
            }

    return EventSourceResponse(generate())


@app.post("/search-location-info")
async def search_location_info_endpoint(params: RequestParams):
    """查询目的地信息"""
    try:
        params_dict = params.model_dump()
        result = await demo_search_location_info(params_dict)
        return {"success": True, "data": result}
    except Exception as e:
        import traceback
        print(f"Error in search_location_info_endpoint: {str(e)}")
        print(f"Traceback: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/get-detailed-plan")
async def get_detailed_plan_endpoint(params: RequestParams):
    """获取详细计划 - SSE 流式响应"""
    async def generate():
        try:
            params_dict = params.model_dump()
            async for content in demo_get_detailed_plan(params_dict):
                yield {
                    "event": "message",
                    "data": json.dumps({"content": content, "type": "detailed_plan"})
                }
            yield {
                "event": "end",
                "data": json.dumps({"message": "详细规划完成"})
            }
        except Exception as e:
            yield {
                "event": "error",
                "data": json.dumps({"error": str(e)})
            }

    return EventSourceResponse(generate())


if __name__ == "__main__":
    import uvicorn
    print("启动约会规划演示服务器...")
    print("访问地址: http://localhost:8000")
    print("API 文档: http://localhost:8000/docs")
    uvicorn.run(app, host="0.0.0.0", port=8000, reload=True)
