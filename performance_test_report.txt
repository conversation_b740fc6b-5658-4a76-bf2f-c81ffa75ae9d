================================================================================
🎯 约会规划服务并发性能测试报告
================================================================================
📅 测试时间: 2025-05-28 17:19:34
🖥️  测试环境: darwin
💾 系统内存: 64.0 GB
🔧 CPU核心数: 16

📊 测试场景 1: 1 并发用户
--------------------------------------------------
⏱️  测试持续时间: 32.85 秒
📈 总请求数: 2
✅ 成功请求: 2
❌ 失败请求: 0
📊 成功率: 100.00%
⚡ 平均响应时间: 16.238 秒
🚀 最快响应时间: 15.110 秒
🐌 最慢响应时间: 17.367 秒
📊 响应时间中位数: 16.238 秒
📈 响应时间标准差: 1.596 秒
💻 平均CPU使用率: 2.3%
💻 最高CPU使用率: 6.4%
💾 平均内存使用率: 30.5%
💾 最高内存使用率: 30.5%
🔥 总QPS (每秒请求数): 0.06
✅ 成功QPS: 0.06
📄 响应内容样本:
   样本 1: ```json
{
    "prologue": "亲爱的张先生、李小姐，05月30日上午9点，广东省深圳市西乡开启一场浪漫约会，届时还请双方准时赴约，提前做好准备，愿你们度过美好时光！",
    "planTitle": "约会详细计划",
    "routePreview": {
        "title": "路线预览",
        "content": {
        ...
   样本 2: ```json
{
  "prologue": "亲爱的张先生、李小姐，2025年5月30日上午9点，深圳市宝安区西乡将迎来一场浪漫约会，请准时在指定地点集合。期待您们共度的美好时光！",
  "planTitle": "约会详细计划",
  "routePreview": {
    "title": "路线预览",
    "content": {
      "table": {
     ...

📊 测试场景 2: 2 并发用户
--------------------------------------------------
⏱️  测试持续时间: 37.30 秒
📈 总请求数: 4
✅ 成功请求: 4
❌ 失败请求: 0
📊 成功率: 100.00%
⚡ 平均响应时间: 16.997 秒
🚀 最快响应时间: 15.110 秒
🐌 最慢响应时间: 18.964 秒
📊 响应时间中位数: 16.957 秒
📈 响应时间标准差: 1.765 秒
💻 平均CPU使用率: 1.8%
💻 最高CPU使用率: 7.0%
💾 平均内存使用率: 30.5%
💾 最高内存使用率: 30.5%
🔥 总QPS (每秒请求数): 0.11
✅ 成功QPS: 0.11
📄 响应内容样本:
   样本 1: ```json
{
    "prologue": "亲爱的张先生、李小姐，05月30日上午9点，广东省深圳市西乡将与对方开启一场浪漫约会，届时还请双方准时赴约，提前做好准备，愿你们度过美好时光！",
    "planTitle": "约会详细计划",
    "routePreview": {
        "title": "路线预览",
        "content": {
    ...
   样本 2: ```json
{
    "prologue": "亲爱的张先生、李小姐，2025年5月30日上午9点，广东省深圳市西乡，将与对方开启一场浪漫约会，届时还请双方准时赴约，提前做好准备，愿你们度过美好时光！",
    "planTitle": "约会详细计划",
    "routePreview": {
        "title": "路线预览",
        "content": {...

📊 测试场景 3: 5 并发用户
--------------------------------------------------
⏱️  测试持续时间: 26.54 秒
📈 总请求数: 5
✅ 成功请求: 5
❌ 失败请求: 0
📊 成功率: 100.00%
⚡ 平均响应时间: 20.878 秒
🚀 最快响应时间: 15.111 秒
🐌 最慢响应时间: 26.339 秒
📊 响应时间中位数: 21.567 秒
📈 响应时间标准差: 4.758 秒
💻 平均CPU使用率: 1.8%
💻 最高CPU使用率: 4.3%
💾 平均内存使用率: 30.5%
💾 最高内存使用率: 30.5%
🔥 总QPS (每秒请求数): 0.19
✅ 成功QPS: 0.19
📄 响应内容样本:
   样本 1: ```json
{
    "prologue": "亲爱的张先生、李小姐，2025年5月30日上午9点，在深圳市西乡开启浪漫约会，愿你们度过美好时光！",
    "planTitle": "详细约会计划",
    "routePreview": {
        "title": "路线预览",
        "content": {
            "table": {
   ...
   样本 2: ```json
{
    "prologue": "亲爱的张先生、李女士，05月30日上午9点，广东省深圳市西乡，将与对方开启一场浪漫约会，请双方准时赴约，提前做好准备，愿你们度过美好时光！",
    "planTitle": "约会详细计划",
    "routePreview": {
        "title": "路线预览",
        "content": {
      ...

📊 测试场景 4: 10 并发用户
--------------------------------------------------
⏱️  测试持续时间: 193.70 秒
📈 总请求数: 10
✅ 成功请求: 10
❌ 失败请求: 0
📊 成功率: 100.00%
⚡ 平均响应时间: 49.051 秒
🚀 最快响应时间: 15.125 秒
🐌 最慢响应时间: 193.490 秒
📊 响应时间中位数: 34.930 秒
📈 响应时间标准差: 51.976 秒
💻 平均CPU使用率: 1.9%
💻 最高CPU使用率: 4.2%
💾 平均内存使用率: 30.5%
💾 最高内存使用率: 30.5%
🔥 总QPS (每秒请求数): 0.05
✅ 成功QPS: 0.05
📄 响应内容样本:
   样本 1: ```json
{
    "prologue": "亲爱的张先生、李小姐，2025年5月30日上午9点，在深圳市宝安区西乡将开启一场浪漫约会，请双方准时赴约并提前做好准备。愿你们度过一段美好的时光！",
    "planTitle": "浪漫约会详细计划",
    "routePreview": {
        "title": "路线预览",
        "content": {
...
   样本 2: ```json
{
    "prologue": "亲爱的张先生、李小姐，2025年5月30日上午9点，广东省深圳市西乡，将与对方开启一场浪漫约会，届时还请双方准时赴约，提前做好准备，愿你们度过美好时光！",
    "planTitle": "约会详细计划",
    "routePreview": {
        "title": "路线预览",
        "content": {...

📊 测试场景 5: 15 并发用户
--------------------------------------------------
⏱️  测试持续时间: 68.86 秒
📈 总请求数: 15
✅ 成功请求: 15
❌ 失败请求: 0
📊 成功率: 100.00%
⚡ 平均响应时间: 41.653 秒
🚀 最快响应时间: 15.717 秒
🐌 最慢响应时间: 68.653 秒
📊 响应时间中位数: 40.412 秒
📈 响应时间标准差: 17.528 秒
💻 平均CPU使用率: 2.3%
💻 最高CPU使用率: 7.3%
💾 平均内存使用率: 30.5%
💾 最高内存使用率: 30.5%
🔥 总QPS (每秒请求数): 0.22
✅ 成功QPS: 0.22
📄 响应内容样本:
   样本 1: ```json
{
    "prologue":"亲爱的张先生、李小姐，2025年5月30日上午9点，广东省深圳市西乡，将与对方开启一场浪漫约会，届时还请双方准时赴约，提前做好准备，愿你们度过美好时光！",
    "planTitle":"约会详细计划",
    "routePreview":{
        "title":"路线预览",
        "content":{
    ...
   样本 2: ```json
{
  "prologue": "亲爱的张先生、李小姐，05月30日上午9点，在广东省深圳市西乡我们将开启一场浪漫的约会，请双方准时赴约并提前做好准备，愿你们度过美好的一天！",
  "planTitle": "详细约会计划",
  "routePreview": {
    "title": "路线预览",
    "content": {
      "table": {
  ...

📊 测试场景 6: 20 并发用户
--------------------------------------------------
⏱️  测试持续时间: 97.87 秒
📈 总请求数: 20
✅ 成功请求: 20
❌ 失败请求: 0
📊 成功率: 100.00%
⚡ 平均响应时间: 50.053 秒
🚀 最快响应时间: 15.369 秒
🐌 最慢响应时间: 97.664 秒
📊 响应时间中位数: 49.432 秒
📈 响应时间标准差: 22.630 秒
💻 平均CPU使用率: 2.4%
💻 最高CPU使用率: 19.5%
💾 平均内存使用率: 30.5%
💾 最高内存使用率: 30.5%
🔥 总QPS (每秒请求数): 0.20
✅ 成功QPS: 0.20
📄 响应内容样本:
   样本 1: ```json
{
    "prologue": "亲爱的张先生、李小姐，2025年05月30日上午9点，广东省深圳市西乡，将与对方开启一场浪漫约会，届时还请双方准时赴约，提前做好准备，愿你们度过美好时光！",
    "planTitle": "约会详细计划",
    "routePreview": {
        "title": "路线预览",
        "content": ...
   样本 2: ```json
{
    "prologue":"亲爱的张先生、李小姐，05月30日上午9点，在深圳市西乡开启一场浪漫约会，请双方准时到达，期待度过一个美好的一天。",
    "planTitle":"约会详细计划",
    "routePreview":{
        "title":"路线预览",
        "content":{
            "table":{
 ...

📊 测试场景 7: 25 并发用户
--------------------------------------------------
⏱️  测试持续时间: 98.33 秒
📈 总请求数: 25
✅ 成功请求: 25
❌ 失败请求: 0
📊 成功率: 100.00%
⚡ 平均响应时间: 57.415 秒
🚀 最快响应时间: 16.323 秒
🐌 最慢响应时间: 98.125 秒
📊 响应时间中位数: 58.179 秒
📈 响应时间标准差: 25.433 秒
💻 平均CPU使用率: 2.6%
💻 最高CPU使用率: 22.0%
💾 平均内存使用率: 30.5%
💾 最高内存使用率: 30.5%
🔥 总QPS (每秒请求数): 0.25
✅ 成功QPS: 0.25
📄 响应内容样本:
   样本 1: ```json
{
  "prologue": "亲爱的张先生、李女士，2025年05月30日上午09点，在广东省深圳市西乡，将与对方开启一场浪漫约会，届时还请双方准时赴约，提前做好准备，愿你们度过美好时光！",
  "planTitle": "约会详细计划",
  "routePreview": {
    "title": "路线预览",
    "content": {
      "tab...
   样本 2: ```json
{
    "prologue": "亲爱的张先生、李小姐，05月30日上午9点，广东省深圳市西乡，将与对方开启一场浪漫约会，届时还请双方准时赴约，提前做好准备，愿你们度过美好时光！",
    "planTitle": "约会详细计划",
    "routePreview": {
        "title": "路线预览",
        "content": {
   ...

📊 测试场景 8: 30 并发用户
--------------------------------------------------
⏱️  测试持续时间: 175.86 秒
📈 总请求数: 30
✅ 成功请求: 29
❌ 失败请求: 1
📊 成功率: 96.67%
⚡ 平均响应时间: 79.584 秒
🚀 最快响应时间: 15.144 秒
🐌 最慢响应时间: 175.650 秒
📊 响应时间中位数: 75.750 秒
📈 响应时间标准差: 44.328 秒
💻 平均CPU使用率: 2.4%
💻 最高CPU使用率: 6.0%
💾 平均内存使用率: 30.5%
💾 最高内存使用率: 30.6%
🔥 总QPS (每秒请求数): 0.17
✅ 成功QPS: 0.16
❌ 错误统计:
   • peer closed connection without sending complete me: 1 次
📄 响应内容样本:
   样本 1: ```json
{
    "prologue": "亲爱的张先生、李小姐，2025年5月30日上午9点，我们将相约在广东省深圳市西乡开启一场浪漫约会。双方务必准时赴约并提前做好准备，愿你们度过一段美好时光！",
    "planTitle": "约会详细计划",
    "routePreview": {
        "title": "路线预览",
        "content": ...
   样本 2: ```json
{
  "prologue": "亲爱的张先生、李小姐，2025年05月30日上午9点，广东省深圳市西乡，将与对方开启一场浪漫约会，届时还请双方准时赴约，提前做好准备，愿你们度过美好时光！",
  "planTitle": "约会详细计划",
  "routePreview": {
    "title": "路线预览",
    "content": {
      "table...

🎯 性能总结
--------------------------------------------------
🏆 最大支持并发数: 30 (成功率≥95%)
🚀 最大成功QPS: 0.25

💡 推荐配置
--------------------------------------------------
🎯 推荐并发数: 24 (安全余量20%)
⚡ 预期QPS: 0.20