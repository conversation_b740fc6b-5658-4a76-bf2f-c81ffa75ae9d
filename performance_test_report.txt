================================================================================
🎯 约会规划服务并发性能测试报告
================================================================================
📅 测试时间: 2025-05-28 14:53:06
🖥️  测试环境: darwin
💾 系统内存: 64.0 GB
🔧 CPU核心数: 16

📊 测试场景 1: 1 并发用户
--------------------------------------------------
⏱️  测试持续时间: 30.27 秒
📈 总请求数: 2
✅ 成功请求: 2
❌ 失败请求: 0
📊 成功率: 100.00%
⚡ 平均响应时间: 14.984 秒
🚀 最快响应时间: 14.648 秒
🐌 最慢响应时间: 15.320 秒
📊 响应时间中位数: 14.984 秒
📈 响应时间标准差: 0.475 秒
💻 平均CPU使用率: 1.5%
💻 最高CPU使用率: 4.9%
💾 平均内存使用率: 28.9%
💾 最高内存使用率: 28.9%
🔥 总QPS (每秒请求数): 0.07
✅ 成功QPS: 0.07
📄 响应内容样本:
   样本 1: ```json
{
    "prologue": "亲爱的张先生、李小姐，05月27日上午9点，广东省深圳市西乡，将与对方开启一场浪漫约会，届时还请双方准时赴约，提前做好准备，愿你们度过美好时光！",
    "planTitle": "约会详细计划",
    "routePreview": {
        "title": "路线预览",
        "content": {
   ...
   样本 2: ```json
{
    "prologue": "亲爱的张先生、李小姐，05月27日上午9点，广东省深圳市西乡将与对方开启一场浪漫约会，届时还请双方准时赴约，提前做好准备，愿你们度过美好时光！",
    "planTitle": "约会详细计划",
    "routePreview": {
        "title": "路线预览",
        "content": {
    ...

📊 测试场景 2: 2 并发用户
--------------------------------------------------
⏱️  测试持续时间: 21.11 秒
📈 总请求数: 2
✅ 成功请求: 2
❌ 失败请求: 0
📊 成功率: 100.00%
⚡ 平均响应时间: 17.650 秒
🚀 最快响应时间: 14.393 秒
🐌 最慢响应时间: 20.907 秒
📊 响应时间中位数: 17.650 秒
📈 响应时间标准差: 4.606 秒
💻 平均CPU使用率: 2.1%
💻 最高CPU使用率: 7.3%
💾 平均内存使用率: 28.8%
💾 最高内存使用率: 28.8%
🔥 总QPS (每秒请求数): 0.09
✅ 成功QPS: 0.09
📄 响应内容样本:
   样本 1: ```json
{
  "prologue": "亲爱的张先生、李小姐，05月27日上午9点，广东省深圳市西乡将与对方开启一场浪漫约会，届时还请双方准时赴约，提前做好准备，愿你们度过美好时光！",
  "planTitle": "约会详细计划",
  "routePreview": {
    "title": "路线预览",
    "content": {
      "table": {
 ...
   样本 2: ```json
{
    "prologue": "亲爱的张先生、李小姐，2025年5月27日上午9点，广东省深圳市西乡将与对方开启一场浪漫约会，届时还请双方准时赴约，提前做好准备，愿你们度过美好时光！",
    "planTitle": "约会详细计划",
    "routePreview": {
        "title": "路线预览",
        "content": {
...

📊 测试场景 3: 5 并发用户
--------------------------------------------------
⏱️  测试持续时间: 25.03 秒
📈 总请求数: 5
✅ 成功请求: 5
❌ 失败请求: 0
📊 成功率: 100.00%
⚡ 平均响应时间: 22.382 秒
🚀 最快响应时间: 18.807 秒
🐌 最慢响应时间: 24.825 秒
📊 响应时间中位数: 24.341 秒
📈 响应时间标准差: 3.029 秒
💻 平均CPU使用率: 2.3%
💻 最高CPU使用率: 6.6%
💾 平均内存使用率: 28.8%
💾 最高内存使用率: 28.8%
🔥 总QPS (每秒请求数): 0.20
✅ 成功QPS: 0.20
📄 响应内容样本:
   样本 1: ```json
{
    "prologue": "亲爱的张先生、李小姐，2025年05月27日上午9点，广东省深圳市西乡，将与对方开启一场浪漫约会，届时还请双方准时赴约，提前做好准备，愿你们度过美好时光！",
    "planTitle": "约会详细计划",
    "routePreview": {
        "title": "路线预览",
        "content": ...
   样本 2: ```json
{
  "prologue": "亲爱的张先生、李小姐，05月27日上午9点，广东省深圳市西乡，将与对方开启一场浪漫约会，届时还请双方准时赴约，提前做好准备，愿你们度过美好时光！",
  "planTitle": "约会详细计划",
  "routePreview": {
    "title": "路线预览",
    "content": {
      "table": {
...

📊 测试场景 4: 10 并发用户
--------------------------------------------------
⏱️  测试持续时间: 29.33 秒
📈 总请求数: 10
✅ 成功请求: 5
❌ 失败请求: 5
📊 成功率: 50.00%
⚡ 平均响应时间: 21.072 秒
🚀 最快响应时间: 11.669 秒
🐌 最慢响应时间: 29.120 秒
📊 响应时间中位数: 21.392 秒
📈 响应时间标准差: 6.957 秒
💻 平均CPU使用率: 3.1%
💻 最高CPU使用率: 12.1%
💾 平均内存使用率: 28.8%
💾 最高内存使用率: 28.8%
🔥 总QPS (每秒请求数): 0.34
✅ 成功QPS: 0.17
❌ 错误统计:
   • Error code: 429 - {'error': {'message': 'You excee: 5 次
📄 响应内容样本:
   样本 1: ```json
{
    "prologue": "亲爱的张先生、李小姐，2025年5月27日上午9点，深圳市西乡将与对方开启一场浪漫约会，届时还请双方准时赴约，提前做好准备，愿你们度过美好时光！",
    "planTitle": "约会详细计划",
    "routePreview": {
        "title": "路线预览",
        "content": {
   ...
   样本 2: ```json
{
    "prologue": "亲爱的张先生、李小姐，2025年5月27日上午9点，广东省深圳市西乡将与对方开启一场浪漫约会，届时还请双方准时赴约，提前做好准备，愿你们度过美好时光！",
    "planTitle": "约会详细计划",
    "routePreview": {
        "title": "路线预览",
        "content": {
...

📊 测试场景 5: 15 并发用户
--------------------------------------------------
⏱️  测试持续时间: 34.62 秒
📈 总请求数: 15
✅ 成功请求: 6
❌ 失败请求: 9
📊 成功率: 40.00%
⚡ 平均响应时间: 23.331 秒
🚀 最快响应时间: 1.694 秒
🐌 最慢响应时间: 34.420 秒
📊 响应时间中位数: 26.253 秒
📈 响应时间标准差: 10.231 秒
💻 平均CPU使用率: 3.0%
💻 最高CPU使用率: 8.3%
💾 平均内存使用率: 28.8%
💾 最高内存使用率: 28.9%
🔥 总QPS (每秒请求数): 0.43
✅ 成功QPS: 0.17
❌ 错误统计:
   • Error code: 429 - {'error': {'message': 'You excee: 7 次
   • 'NoneType' object is not subscriptable: 2 次
📄 响应内容样本:
   样本 1: ```json
{
    "prologue":"亲爱的张先生、李小姐，2025年5月27日上午9点，广东省深圳市西乡，将开启一场浪漫约会，届时请双方准时赴约，愿你们度过美好的时光！",
    "planTitle":"约会详细计划",
    "routePreview":{
        "title":"路线预览",
        "content":{
            "t...
   样本 2: ```json
{
    "prologue": "亲爱的张先生、李小姐，2025年5月27日上午9点，广东省深圳市西乡，将与对方开启一场浪漫约会，届时还请双方准时赴约，提前做好准备，愿你们度过美好时光！",
    "planTitle": "约会详细计划",
    "routePreview": {
        "title": "路线预览",
        "content": {...

🎯 性能总结
--------------------------------------------------
🏆 最大支持并发数: 5 (成功率≥95%)
🚀 最大成功QPS: 0.20

💡 推荐配置
--------------------------------------------------
🎯 推荐并发数: 4 (安全余量20%)
⚡ 预期QPS: 0.16