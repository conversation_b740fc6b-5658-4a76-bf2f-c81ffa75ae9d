================================================================================
🎯 约会规划服务并发性能测试报告
================================================================================
📅 测试时间: 2025-05-28 11:20:25
🖥️  测试环境: darwin
💾 系统内存: 64.0 GB
🔧 CPU核心数: 16

📊 测试场景 1: 1 并发用户
--------------------------------------------------
⏱️  测试持续时间: 20.11 秒
📈 总请求数: 83
✅ 成功请求: 0
❌ 失败请求: 83
📊 成功率: 0.00%
⚡ 平均响应时间: 0.124 秒
🚀 最快响应时间: 0.105 秒
🐌 最慢响应时间: 0.189 秒
📊 响应时间中位数: 0.110 秒
📈 响应时间标准差: 0.030 秒
💻 平均CPU使用率: 3.6%
💻 最高CPU使用率: 21.0%
💾 平均内存使用率: 24.6%
💾 最高内存使用率: 24.7%
🔥 总QPS (每秒请求数): 4.13
✅ 成功QPS: 0.00
❌ 错误统计:
   • 'NoneType' object is not subscriptable: 83 次

📊 测试场景 2: 2 并发用户
--------------------------------------------------
⏱️  测试持续时间: 20.17 秒
📈 总请求数: 120
✅ 成功请求: 0
❌ 失败请求: 120
📊 成功率: 0.00%
⚡ 平均响应时间: 0.224 秒
🚀 最快响应时间: 0.211 秒
🐌 最慢响应时间: 0.289 秒
📊 响应时间中位数: 0.216 秒
📈 响应时间标准差: 0.021 秒
💻 平均CPU使用率: 2.8%
💻 最高CPU使用率: 16.3%
💾 平均内存使用率: 24.3%
💾 最高内存使用率: 24.5%
🔥 总QPS (每秒请求数): 5.95
✅ 成功QPS: 0.00
❌ 错误统计:
   • 'NoneType' object is not subscriptable: 120 次

📊 测试场景 3: 5 并发用户
--------------------------------------------------
⏱️  测试持续时间: 20.31 秒
📈 总请求数: 155
✅ 成功请求: 0
❌ 失败请求: 155
📊 成功率: 0.00%
⚡ 平均响应时间: 0.544 秒
🚀 最快响应时间: 0.528 秒
🐌 最慢响应时间: 0.607 秒
📊 响应时间中位数: 0.538 秒
📈 响应时间标准差: 0.021 秒
💻 平均CPU使用率: 3.2%
💻 最高CPU使用率: 8.6%
💾 平均内存使用率: 24.2%
💾 最高内存使用率: 24.2%
🔥 总QPS (每秒请求数): 7.63
✅ 成功QPS: 0.00
❌ 错误统计:
   • 'NoneType' object is not subscriptable: 155 次

📊 测试场景 4: 10 并发用户
--------------------------------------------------
⏱️  测试持续时间: 20.62 秒
📈 总请求数: 170
✅ 成功请求: 0
❌ 失败请求: 170
📊 成功率: 0.00%
⚡ 平均响应时间: 1.066 秒
🚀 最快响应时间: 1.054 秒
🐌 最慢响应时间: 1.080 秒
📊 响应时间中位数: 1.066 秒
📈 响应时间标准差: 0.006 秒
💻 平均CPU使用率: 1.2%
💻 最高CPU使用率: 6.5%
💾 平均内存使用率: 24.2%
💾 最高内存使用率: 24.2%
🔥 总QPS (每秒请求数): 8.24
✅ 成功QPS: 0.00
❌ 错误统计:
   • 'NoneType' object is not subscriptable: 170 次

🎯 性能总结
--------------------------------------------------
🏆 最大支持并发数: 0 (成功率≥95%)
🚀 最大成功QPS: 0.00

💡 推荐配置
--------------------------------------------------
🎯 推荐并发数: 1 (安全余量20%)
⚡ 预期QPS: 0.00