#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
@File	:dating_planner.py
@Time	:2025/05/15 18:08:28
<AUTHOR>
@Mail	:<EMAIL>
"""

import asyncio
from urllib.parse import uses_params

from httpx import request
from tools import weather_finder, traffic_finder, movies_finder, poi_seacher
from tools import PoiInput, WeatherInput, TrafficInput, MoviesInput
from prompt import DATING_PLAN_PROMPT, DATING_PLAN_FROM_FRONTEND_PROMPT, CUSTOM_CHAT_PROMPT
from llm import async_client
from logger import LOGGER
from const import TODAY
from location_helper import LocationHelper
from typing import Dict, Any

# 导入缓存函数
from cache import cached_poi_search


class DatingPlanner:

    def __init__(self):
        self.location_helper = LocationHelper()

    async def extract_to_location_detail_from_user_input(self, user_input: str, user_lat_lng: str):
        """
        从用户输入提取用户目的地的详细信息
        Args:
            user_input (str): 用户输入的原始文本
            user_lat_lng (str): 用户的经纬度坐标
        Returns:
            dict: 包含目的地详细信息的字典，包括：
                - to_location_detail (str): 目的地的详细地址
                - to_location_adcode (str): 目的地的行政区划代码
                - to_location_lat (float): 目的地的纬度
                - to_location_lng (float): 目的地的经度
        """
        to_location_detail_info_task = self.location_helper.extract_to_location_detail(
            user_input, user_lat_lng)
        user_location_info_task = self.location_helper.lat_lng2city(
            user_lat_lng)

        to_location_detail_info, user_location_info = await asyncio.gather(
            to_location_detail_info_task,
            user_location_info_task
        )
        # to_location_detail_info = await to_location_detail_info_task
        # user_location_info = await user_location_info_task

        user_location_province = user_location_info['province']
        user_location_city = user_location_info['city']
        user_location_district = user_location_info['district']

        # user_location_detail_info_task = self.location_helper.extract_to_location_detail(user_input, user_lat_lng)
        to_location_detail = to_location_detail_info['to_location_detail']
        to_location_name = to_location_detail_info['to_location_detail_mark'].split('|')[
            1]
        to_location_date = to_location_detail_info['to_location_date']

        # # to_location_adcode = to_location_detail_info['to_location_adcode']
        # to_location_lat_lng = await self.location_helper.to_location2lat_lng(to_location_detail)
        # to_location_lat_lng = to_location_lat_lng['location']
        # # to_location_province = await self.location_helper.to_location2lat_lng(to_location_detail)['province']
        # # to_location_city = await self.location_helper.to_location2lat_lng(to_location_detail)['city']
        # to_location_adcode_info = await self.location_helper.lat_lng2city(to_location_lat_lng)
        # to_location_adcode = to_location_adcode_info['adcode']
        # to_location_city = to_location_adcode_info['city']
        # to_location_province = to_location_adcode_info['province']

        to_location_result = await self.location_helper.to_location2lat_lng(to_location_detail)
        if to_location_result is None:
            # 如果地理位置查询失败，使用默认值
            LOGGER.warning(f"地理位置查询失败，使用默认坐标: {to_location_detail}")
            to_location_lat_lng = "114.085,22.547"  # 深圳默认坐标
            to_location_province = "广东省"
            to_location_city = "深圳市"
            to_location_adcode = "440300"
        else:
            to_location_lat_lng = to_location_result['location']
            # to_location_province = await self.location_helper.to_location2lat_lng(to_location_detail)['province']
            # to_location_city = await self.location_helper.to_location2lat_lng(to_location_detail)['city']
            to_location_adcode_info = await self.location_helper.lat_lng2city(to_location_lat_lng)
            to_location_adcode = to_location_adcode_info['adcode']
            to_location_city = to_location_adcode_info['city']
            to_location_province = to_location_adcode_info['province']

        # user_location
        to_location_adcode_info = await self.location_helper.lat_lng2city(to_location_lat_lng)
        to_location_adcode = to_location_adcode_info['adcode']
        to_location_city = to_location_adcode_info['city']
        to_location_province = to_location_adcode_info['province']

        return {
            'to_location_province': to_location_province,
            'to_location_city': to_location_city,
            'to_location_detail': to_location_detail,
            'to_location_adcode': to_location_adcode,
            'to_location_lat_lng': to_location_lat_lng,
            'user_location_province': user_location_province,
            'user_location_city': user_location_city,
            'user_location_district': user_location_district,
            'to_location_name': to_location_name,
            'to_location_date': to_location_date
        }

    async def dating_planner(
        self,
        # user_input,
        # user_lat_lng,
        # # to_location,
        # # to_location_lat,
        # # to_location_lng,
        # dating_date,
        # # user_location,
        # # user_location_lat,
        # # user_location_lng,
        frontend_params
    ):
        user_input = frontend_params['user_input']
        request_type = frontend_params['request_type']
        user_lat_lng = frontend_params['dating_params']['user_lat_lng']

        if request_type == 'dating':
            to_location_detail = {
                # 广东省深圳市COCO Park
                'to_location_detail': frontend_params['dating_params']['to_location_province'] + frontend_params['dating_params']['to_location_city'] + frontend_params['dating_params']['to_location_name'],
                # 广东省深圳市|COCO Park
                'to_location_detail_mark': frontend_params['dating_params']['to_location_province'] + frontend_params['dating_params']['to_location_city'] + '|' + frontend_params['dating_params']['to_location_name'],
                # 2025-05-25
                'to_location_date': frontend_params['dating_params']['date']
            }

            LOGGER.info('正在查询目的地经纬度坐标...')
            to_location_lat_lng = await self.location_helper.to_location2lat_lng(to_location_detail['to_location_detail'])
            to_location_lat_lng = to_location_lat_lng['location']
            LOGGER.info('正在查询目的地行政区划代码...')
            to_location_adcode_info = await self.location_helper.lat_lng2city(to_location_lat_lng)
            to_location_adcode = to_location_adcode_info['adcode']

            to_location_detail_info = {
                'to_location_province': frontend_params['dating_params']['to_location_province'],
                'to_location_city': frontend_params['dating_params']['to_location_city'],
                'to_location_detail': to_location_detail,
                'to_location_adcode': to_location_adcode,
                'to_location_lat_lng': to_location_lat_lng,
                'user_location_province': frontend_params['dating_params']['user_location_province'],
                'user_location_city': frontend_params['dating_params']['user_location_city'],
                'user_location_district': frontend_params['dating_params']['user_location_district'],
                'to_location_name': frontend_params['dating_params']['to_location_name'],
                'to_location_date': frontend_params['dating_params']['date'],
                'to_location_time_start': frontend_params['dating_params']['time_start'],
                'to_location_time_end': frontend_params['dating_params']['time_end']
            }

            poi_input = {"params": PoiInput(location_helper=self.location_helper,
                                            to_location_detail=to_location_detail['to_location_detail'], to_location_lat_lng=to_location_lat_lng)}
            weather_input = {"params": WeatherInput(
                location=to_location_detail_info['to_location_city'], date=to_location_detail_info['to_location_date'], to_location_adcode=to_location_adcode)}
            user_traffic_input = {"params": TrafficInput(
                origin=user_lat_lng, destination=to_location_lat_lng, mode=frontend_params['dating_params'][
                    'trans_tool_user'], city=to_location_detail_info['user_location_city'], city_d=to_location_detail_info['to_location_city']
            )}
            comp_traffic_input = {"params": TrafficInput(
                origin=frontend_params['dating_params']['comp_lat_lng'], destination=to_location_lat_lng, mode=frontend_params['dating_params'][
                    'trans_tool_comp'], city=frontend_params['dating_params']['comp_location_city'], city_d=to_location_detail_info['to_location_city']
            )}

            movies_input = {
                "params": MoviesInput(
                    to_location=to_location_detail_info['to_location_name'],
                    to_location_lat=to_location_lat_lng.split(',')[
                        1].strip(),
                    to_location_lng=to_location_lat_lng.split(',')[
                        0].strip(),
                    watch_movie_date=to_location_detail_info['to_location_date'],
                )
            }

            # 使用缓存的POI查询结果，避免重复查询
            poi_task = cached_poi_search(poi_seacher, poi_input)
            weather_task = weather_finder.ainvoke(weather_input)
            user_traffic_task = traffic_finder.ainvoke(user_traffic_input)
            comp_traffic_task = traffic_finder.ainvoke(comp_traffic_input)
            movies_task = movies_finder.ainvoke(movies_input)

            LOGGER.info('正在并行查询POI、天气、交通、电影信息...')
            poi_result, weather_result, user_traffic_result, comp_traffic_result, movies_result = await asyncio.gather(
                poi_task,
                weather_task,
                user_traffic_task,
                comp_traffic_task,
                movies_task
            )
            # poi_result, movies_result = await asyncio.gather(
            #     poi_task,
            #     movies_task
            # )
            # weather_result = await weather_task
            # user_traffic_result = await user_traffic_task
            # comp_traffic_result = await comp_traffic_task
            

            # 安全地提取POI结果，处理可能的错误情况
            if isinstance(poi_result, dict) and 'to_location_food' in poi_result:
                # 检查每个POI类型的结果是否为列表
                to_location_food = poi_result['to_location_food']
                to_location_shopping = poi_result['to_location_shopping']
                to_location_entertainment = poi_result['to_location_entertainment']
                to_location_sightseeing = poi_result['to_location_sightseeing']

                # 验证每个结果是否为有效的列表，如果是错误字典则使用空列表
                if not isinstance(to_location_food, list) or (isinstance(to_location_food, dict) and 'status' in to_location_food):
                    LOGGER.warning(f"POI美食查询失败: {to_location_food}")
                    to_location_food = []
                    import sys
                    sys.exit()

                if not isinstance(to_location_shopping, list) or (isinstance(to_location_shopping, dict) and 'status' in to_location_shopping):
                    LOGGER.warning(f"POI购物查询失败: {to_location_shopping}")
                    to_location_shopping = []
                    import sys
                    sys.exit()

                if not isinstance(to_location_entertainment, list) or (isinstance(to_location_entertainment, dict) and 'status' in to_location_entertainment):
                    LOGGER.warning(f"POI娱乐查询失败: {to_location_entertainment}")
                    to_location_entertainment = []
                    import sys
                    sys.exit()

                if not isinstance(to_location_sightseeing, list) or (isinstance(to_location_sightseeing, dict) and 'status' in to_location_sightseeing):
                    LOGGER.warning(f"POI景点查询失败: {to_location_sightseeing}")
                    to_location_sightseeing = []
                    import sys
                    sys.exit()
            else:
                # 如果POI查询失败，使用空列表作为默认值
                LOGGER.warning(f"POI查询失败，返回结果: {poi_result}")
                to_location_food: list = []
                to_location_shopping: list = []
                to_location_entertainment: list = []
                to_location_sightseeing: list = []

            def get_poi_need_info(poi_res: list):
                """
                获取POI的名称、类型、经纬度
                """
                new_res = []
                if not poi_res:
                    return new_res

                for poi in poi_res:
                    try:
                        # 安全地提取POI信息，处理可能缺失的字段
                        poi_info = {
                            'name': poi.get('name', '未知地点'),
                            'type': poi.get('type', '未知类型'),
                            'location': poi.get('location', ''),
                            'address': poi.get('address', '未知地址')
                        }
                        new_res.append(poi_info)
                    except (TypeError, AttributeError) as e:
                        LOGGER.warning(f"处理POI数据时出错: {e}, POI数据: {poi}")
                        continue
                return new_res

            to_location_food = get_poi_need_info(to_location_food)
            to_location_shopping = get_poi_need_info(to_location_shopping)
            to_location_entertainment = get_poi_need_info(
                to_location_entertainment)
            to_location_sightseeing = get_poi_need_info(
                to_location_sightseeing)
            # poi_result = {
            #     'to_location_food': to_location_food,
            #     'to_location_shopping': to_location_shopping,
            #     'to_location_entertainment': to_location_entertainment,
            #     'to_location_sightseeing': to_location_sightseeing
            # }

            dating_times_dict = {
                0: "首次",
                1: "第二次",
                2: "多次",
                3: "已是恋人关系"
            }
            dating_times = dating_times_dict[frontend_params['dating_params']
                                             ['dating_times']]
            context = {
                "to_location_detail": to_location_detail,
                "to_location_weather": weather_result,
                "user_traffic": user_traffic_result,
                "comp_traffic": comp_traffic_result,
                "to_location_restaurants": to_location_food,
                "to_location_shopping": to_location_shopping,
                "to_location_entertainment": to_location_entertainment,
                "to_location_sightseeing": to_location_sightseeing,
                "to_location_movies": movies_result,
                "user_origin": frontend_params['dating_params']['user_location_city'] + frontend_params['dating_params']['user_location_district'],
                "comp_origin": frontend_params['dating_params']['comp_location_city'] + frontend_params['dating_params']['comp_location_district'],
                "male_last_name": frontend_params['dating_params']['male_last_name'],
                "female_last_name": frontend_params['dating_params']['female_last_name'],
                "trans_tool_user": frontend_params['dating_params']['trans_tool_user'],
                "trans_tool_comp": frontend_params['dating_params']['trans_tool_comp'],
                "date": frontend_params['dating_params']['date'],
                "time_start": frontend_params['dating_params']['time_start'],
                "time_end": frontend_params['dating_params']['time_end'],
                "dating_times": dating_times,
                "dating_type": frontend_params['dating_params']['dating_type'],
                "more_thoughts": frontend_params['dating_params']['more_thoughts'],
                "today": TODAY
            }

            # 构建完整的提示
            full_prompt = DATING_PLAN_FROM_FRONTEND_PROMPT.format(**context)

            LOGGER.info('正在使用LLM生成约会计划...')
            stream = await async_client.chat.completions.create(
                model="qwen-plus-2025-01-12",
                messages=[{"role": "system", "content": full_prompt}],
                temperature=1.3,
                stream=True,
            )

            full_content = ""
            async for chunk in stream:
                if chunk.choices[0].delta.content is not None:
                    content = chunk.choices[0].delta.content
                    yield content
                    # print(content, end="", flush=True)
                    # full_content += content

            # print("\n")
            # LOGGER.info(full_content)
            # return full_content

        else:
            want_to_dating = False  # 意图识别结果
            if want_to_dating:

                # dating_date = '2025-05-23' # 用大模型识别user_input中的约会日期,没有日期使用TODAY

                location_detail = await self.extract_to_location_detail_from_user_input(user_input=user_input, user_lat_lng=user_lat_lng)
                to_location_province = location_detail['to_location_province']
                to_location_city = location_detail['to_location_city']
                to_location_detail = location_detail['to_location_detail']
                to_location_adcode = location_detail['to_location_adcode']
                to_location_lat_lng = location_detail['to_location_lat_lng']
                to_location_lat_lng_lst = to_location_lat_lng.split(',')
                to_location_lat = to_location_lat_lng_lst[1].strip()
                to_location_lng = to_location_lat_lng_lst[0].strip()
                user_location_province = location_detail['user_location_province']
                user_location_city = location_detail['user_location_city']
                user_location_district = location_detail['user_location_district']
                to_location_name = location_detail['to_location_name']
                dating_date = location_detail['to_location_date']

                # poi_input = {"params": FoodsInput(user_lat_lng=)}
                poi_input = {"params": PoiInput(
                    location_helper=self.location_helper, to_location_detail=to_location_detail, to_location_lat_lng=to_location_lat_lng)}

                weather_input = {"params": WeatherInput(
                    location=to_location_city, date=dating_date, to_location_adcode=to_location_adcode)}
                # traffic_input = {
                #     "params": TrafficInput(origin=user_location, destination=to_location)
                # }
                traffic_input = {"params": TrafficInput(
                    origin=user_lat_lng, destination=to_location_lat_lng, mode='bus', city=user_location_city, city_d=to_location_city)}
                movies_input = {
                    "params": MoviesInput(
                        to_location=to_location_name,
                        to_location_lat=to_location_lat,
                        to_location_lng=to_location_lng,
                        watch_movie_date=dating_date,
                    )
                }

                # 使用 gather 并行执行所有查询
                print("===== 开始并行查询 =====\n")
                tasks = [
                    poi_seacher.ainvoke(poi_input),
                    weather_finder.ainvoke(weather_input),
                    traffic_finder.ainvoke(traffic_input),
                    movies_finder.ainvoke(movies_input),
                ]

                # 等待所有任务完成
                results = await asyncio.gather(*tasks)

                # 打印结果
                print("\n===== POI查询结果 =====")
                print(results[0])
                food_info = results[0]['to_location_food']
                shopping_info = results[0]['to_location_shopping']
                entertainment_info = results[0]['to_location_entertainment']
                sightseeing_info = results[0]['to_location_sightseeing']

                print("\n===== 天气查询结果 =====")
                print(results[1])
                weather_info = results[1]

                print("\n===== 交通查询结果 =====")
                print(results[2])
                traffic_info = results[2]

                print("\n===== 电影查询结果 =====")
                print(results[3])
                movies_info = results[3]

                # 构建输入上下文
                context = {
                    "to_location_detail": to_location_detail,
                    "to_location_weather": weather_info,
                    "to_location_traffic": traffic_info,
                    "to_location_restaurants": food_info,
                    "to_location_shopping": shopping_info,
                    "to_location_entertainment": entertainment_info,
                    "to_location_sightseeing": sightseeing_info,
                    "to_location_movies": movies_info,
                    "origin": user_location_city+user_location_district,
                    # "origin_lat": user_location_lat,
                    # "origin_lng": user_location_lng,
                    "date": dating_date,
                    "today": TODAY
                }

                # 构建完整的提示
                full_prompt = DATING_PLAN_PROMPT.format(**context)

                # 调用 LLM（流式输出）
                stream = await async_client.chat.completions.create(
                    model="qwen-plus-2025-01-12",
                    messages=[{"role": "system", "content": full_prompt}],
                    stream=True,
                )

                full_content = ""
                async for chunk in stream:
                    if chunk.choices[0].delta.content is not None:
                        content = chunk.choices[0].delta.content
                        yield content
                        # print(content, end="", flush=True)
                        # full_content += content

                # print("\n")
                # LOGGER.info(full_content)
                # return full_content
            else:
                # TODO 普通聊天
                from performance_optimization import optimizer
                
                # 使用OpenAI API专用信号量控制并发
                async with optimizer.openai_api_semaphore:
                    stream = await async_client.chat.completions.create(
                        model="qwen-plus-2025-01-12",
                        messages=[{"role": "system", "content": CUSTOM_CHAT_PROMPT}, {
                            "role": "user", "content": user_input}],
                        stream=True,
                    )

                    # full_content = ""
                    async for chunk in stream:
                        if chunk.choices[0].delta.content is not None:
                            content = chunk.choices[0].delta.content
                            yield content
                #         print(content, end="", flush=True)
                #         full_content += content

                # print("\n")
                # LOGGER.info(full_content)
                # return full_content


if __name__ == "__main__":
    async def main_wrapper(async_gen):
        async for chunk in async_gen:
            print(chunk, end="", flush=True)
    params = {
        'user_input': '怎样避免约会的时候双方陷入沉默?',  # 用户输入(按钮点选则此字段为空字符串)
        'request_type': 'dating',  # 请求类型 'dating':约会规划，'chat':对话聊天
        'dating_params': {
            'to_location_province': '广东省',  # 目的地省份
            'to_location_city': '深圳市',  # 目的地城市
            'to_location_district': '宝安区',  # 目的地区县
            'to_location_name': '西乡',  # 目的地名称

            'user_lat_lng': '113.999706,22.588863',  # 用户位置经纬度
            'user_location_province': '广东省',  # 用户位置省份
            'user_location_city': '深圳市',  # 用户位置城市
            'user_location_district': '南山区',  # 用户位置区县
            'user_location_name': '塘朗城广场',  # 用户位置名称

            'departure_lat_lng': '113.999706,22.588863',  # 出发地经纬度
            'departure_province': '广东省',  # 出发地省份
            'departure_city': '深圳市',  # 出发地城市
            'departure_district': '南山区',  # 出发地区县
            'departure_name': '塘朗城广场',  # 出发地名称

            'comp_lat_lng': '114.142917,22.620833',  # 伴侣位置经纬度
            'comp_location_province': '广东省',  # 伴侣位置省份
            'comp_location_city': '深圳市',  # 伴侣位置城市
            'comp_location_district': '龙岗区',  # 伴侣位置区县
            'comp_location_name': '怡乐花园',  # 伴侣位置名称

            'male_last_name': '张',  # 男方姓氏
            'female_last_name': '李',  # 女方姓氏

            'trans_tool_user': 'bus',  # 用户交通方式 bus-公交 car-自驾
            'trans_tool_comp': 'car',  # 伴侣交通方式 bus-公交 car-自驾

            'date': '2025-05-27',  # 约会日期
            'time_start': '09:00',  # 约会开始时间
            'time_end': '20:00',  # 约会结束时间，

            'dating_times': 3,  # 约会次数 1-1次 2-2次 3-3多次 4-确认关系

            'dating_type': '浪漫型',  # 约会偏好 浪漫型/趣味型/文艺型

            'more_thoughts': '我希望约会时可以吃到火锅'  # 更多想法
        }
    }
    dating_planner = DatingPlanner()
    # asyncio.run(
    #     dating_planner.dating_planner(
    #         # user_input="这周六下午我想和女朋友在COCO Park约会",
    #         # user_lat_lng="113.999706,22.588863",
    #         # dating_date="2025-05-22",
    #         # to_location="COCO Park",
    #         # to_location_lat=22.520922,
    #         # to_location_lng=114.055198,
    #         # dating_date="2025-05-17",
    #         # user_location="塘朗城广场",
    #         # user_location_lat=22.588863,
    #         # user_location_lng=113.999706,
    #         frontend_params={
    #             'user_input': '这周日下午我想和女朋友在COCO Park约会',
    #             'request_type': 'chat',
    #             'dating_params': {
    #                 'user_lat_lng': '113.999706,22.588863',

    #             }
    #         }
    #     )
    # )

    asyncio.run(
        main_wrapper(dating_planner.dating_planner(
            frontend_params=params
        ))
    )
