#!/usr/bin/env python
# -*- coding: utf-8 -*-
'''
@File	:llm.py
@Time	:2025/05/14 17:24:27
<AUTHOR>
@Mail	:<EMAIL>
'''


from openai import AsyncOpenAI, OpenAI

import os
from typing import Optional
from langchain.pydantic_v1 import BaseModel, Field
from langchain_core.tools import tool
from langchain_openai import ChatOpenAI
from prompt import THINK_PLAN_PROMPT


api_key = os.environ.get('DASH_SCOPE_API_KEY')

if not api_key or api_key.startswith('sk-placeholder'):
    print("⚠️  警告: 未设置有效的 DASH_SCOPE_API_KEY")
    print("请在 .env 文件中设置您的真实 API 密钥")
    # 使用占位符密钥，但会在实际调用时失败
    api_key = "sk-placeholder-key"

async_client = AsyncOpenAI(
    api_key=api_key,
    base_url="https://dashscope.aliyuncs.com/compatible-mode/v1"
)

sync_client = OpenAI(
    api_key=api_key,
    base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",
)


chat_openai_client = ChatOpenAI(
    api_key=api_key,
    base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",
    model="qwen-plus-2025-01-12"
)

dating_plan_llm = ChatOpenAI(
    api_key=api_key,
    base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",
    model="qwen-plus-2025-01-12",
    temperature=0.9
)

# async def main():
#     know_info = {
#         'know_info':'用户出发地:南山区、自驾;伴侣出发地:福田区、公交;约会时间:2025-05-26 14:00-17:00;约会区域范围为福田区、COCO Park;约会格调为浪漫型'
#     }
#     full_prompt = THINK_PLAN_PROMPT.format(**know_info)
#     res = await async_client.chat.completions.create(model="qwen-plus", messages=[{"role": "system", "content": full_prompt}], stream=True)
#     async for chunk in res:
#         if chunk.choices[0].delta.content is not None:
#             content = chunk.choices[0].delta.content
#             print(content, end="", flush=True)


# if __name__ == "__main__":
#     import asyncio
#     asyncio.run(main())
