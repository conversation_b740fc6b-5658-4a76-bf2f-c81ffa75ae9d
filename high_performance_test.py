#!/usr/bin/env python3
"""
高强度性能测试脚本 - 临时禁用限流版本
用于测试服务器的真实性能上限
"""

import asyncio
import aiohttp
import time
import json
import statistics
from typing import List, Dict

class HighPerformanceTester:
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.test_data = {
            "user_params": {
                "age": 25,
                "gender": "男",
                "interests": ["电影", "美食"],
                "personality": "外向",
                "budget": 500
            },
            "dating_params": {
                "from_location_province": "北京市",
                "from_location_city": "北京市",
                "from_location_name": "朝阳区",
                "to_location_province": "北京市", 
                "to_location_city": "北京市",
                "to_location_name": "故宫",
                "dating_time": "2025-05-30 09:00-20:00",
                "dating_area": "故宫周边",
                "dating_type": "浪漫型"
            }
        }

    async def disable_rate_limiting(self):
        """临时禁用限流 - 通过特殊端点"""
        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(f"{self.base_url}/admin/disable-rate-limit") as response:
                    if response.status == 200:
                        print("✅ 已临时禁用限流")
                        return True
        except:
            pass
        print("⚠️  无法禁用限流，将使用当前设置进行测试")
        return False

    async def enable_rate_limiting(self):
        """重新启用限流"""
        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(f"{self.base_url}/admin/enable-rate-limit") as response:
                    if response.status == 200:
                        print("✅ 已重新启用限流")
                        return True
        except:
            pass
        return False

    async def single_request(self, session: aiohttp.ClientSession, endpoint: str) -> Dict:
        """发送单个请求"""
        start_time = time.time()
        try:
            url = f"{self.base_url}/{endpoint}"
            async with session.post(url, json=self.test_data) as response:
                if response.status == 200:
                    # 读取SSE响应
                    content = ""
                    async for line in response.content:
                        line_str = line.decode('utf-8').strip()
                        if line_str.startswith('data: '):
                            try:
                                data = json.loads(line_str[6:])
                                if data.get('content'):
                                    content += data['content']
                            except:
                                pass
                    
                    duration = time.time() - start_time
                    return {"success": True, "duration": duration, "content_length": len(content)}
                else:
                    duration = time.time() - start_time
                    return {"success": False, "duration": duration, "status": response.status}
        except Exception as e:
            duration = time.time() - start_time
            return {"success": False, "duration": duration, "error": str(e)}

    async def concurrent_test(self, endpoint: str, concurrent_users: int, total_requests: int) -> Dict:
        """并发测试"""
        print(f"\n🚀 测试 {endpoint} - 并发: {concurrent_users}, 总请求: {total_requests}")
        
        connector = aiohttp.TCPConnector(limit=concurrent_users * 2)
        timeout = aiohttp.ClientTimeout(total=60)
        
        async with aiohttp.ClientSession(connector=connector, timeout=timeout) as session:
            # 创建任务
            tasks = []
            for i in range(total_requests):
                task = self.single_request(session, endpoint)
                tasks.append(task)
            
            # 控制并发数
            semaphore = asyncio.Semaphore(concurrent_users)
            
            async def limited_request(task):
                async with semaphore:
                    return await task
            
            # 执行测试
            start_time = time.time()
            results = await asyncio.gather(*[limited_request(task) for task in tasks], return_exceptions=True)
            total_duration = time.time() - start_time
            
            # 分析结果
            successful_results = [r for r in results if isinstance(r, dict) and r.get("success")]
            failed_results = [r for r in results if not (isinstance(r, dict) and r.get("success"))]
            
            response_times = [r["duration"] for r in successful_results]
            
            if response_times:
                response_time_stats = {
                    "avg": statistics.mean(response_times),
                    "min": min(response_times),
                    "max": max(response_times),
                    "p50": statistics.median(response_times),
                    "p95": statistics.quantiles(response_times, n=20)[18] if len(response_times) > 20 else max(response_times),
                    "p99": statistics.quantiles(response_times, n=100)[98] if len(response_times) > 100 else max(response_times)
                }
            else:
                response_time_stats = {"avg": 0, "min": 0, "max": 0, "p50": 0, "p95": 0, "p99": 0}
            
            return {
                "endpoint": endpoint,
                "concurrent_users": concurrent_users,
                "total_requests": total_requests,
                "successful_requests": len(successful_results),
                "failed_requests": len(failed_results),
                "success_rate": len(successful_results) / total_requests * 100,
                "total_duration": total_duration,
                "requests_per_second": total_requests / total_duration,
                "response_times": response_time_stats,
                "errors": [str(r) for r in failed_results if isinstance(r, Exception)][:5]
            }

    async def run_high_performance_test(self):
        """运行高强度性能测试"""
        print("=" * 80)
        print("🚀 约会规划服务 - 高强度性能测试")
        print("=" * 80)
        
        # 尝试禁用限流
        await self.disable_rate_limiting()
        
        try:
            # 高强度测试配置
            test_configs = [
                {"endpoint": "explain-request", "concurrent": 20, "total": 100},
                {"endpoint": "explain-request", "concurrent": 50, "total": 200},
                {"endpoint": "think-plan", "concurrent": 15, "total": 75},
                {"endpoint": "think-plan", "concurrent": 30, "total": 150},
                {"endpoint": "search-location-info", "concurrent": 10, "total": 50},
                {"endpoint": "search-location-info", "concurrent": 20, "total": 100},
                {"endpoint": "get-detailed-plan", "concurrent": 8, "total": 40},
                {"endpoint": "get-detailed-plan", "concurrent": 15, "total": 75},
            ]
            
            results = []
            
            for config in test_configs:
                try:
                    result = await self.concurrent_test(
                        config["endpoint"],
                        config["concurrent"],
                        config["total"]
                    )
                    results.append(result)
                    
                    # 打印结果
                    print(f"📊 成功率: {result['success_rate']:.1f}% | "
                          f"QPS: {result['requests_per_second']:.1f} | "
                          f"P95: {result['response_times']['p95']:.2f}s")
                    
                    # 短暂休息
                    await asyncio.sleep(1)
                    
                except Exception as e:
                    print(f"❌ 测试失败: {str(e)}")
            
            # 生成报告
            self.generate_high_performance_report(results)
            
        finally:
            # 重新启用限流
            await self.enable_rate_limiting()

    def generate_high_performance_report(self, results: List[Dict]):
        """生成高强度性能测试报告"""
        print("\n" + "=" * 80)
        print("📈 高强度性能测试报告")
        print("=" * 80)
        
        if not results:
            print("❌ 没有测试结果")
            return
        
        # 按端点分组
        endpoints = {}
        for result in results:
            endpoint = result['endpoint']
            if endpoint not in endpoints:
                endpoints[endpoint] = []
            endpoints[endpoint].append(result)
        
        # 详细报告
        for endpoint, endpoint_results in endpoints.items():
            print(f"\n🎯 {endpoint.upper()} 端点性能分析")
            print("-" * 60)
            
            for result in endpoint_results:
                print(f"   并发: {result['concurrent_users']:2d} | "
                      f"成功率: {result['success_rate']:5.1f}% | "
                      f"QPS: {result['requests_per_second']:6.1f} | "
                      f"P50: {result['response_times']['p50']:5.2f}s | "
                      f"P95: {result['response_times']['p95']:5.2f}s | "
                      f"P99: {result['response_times']['p99']:5.2f}s")
        
        # 综合分析
        print(f"\n📊 综合性能分析")
        print("=" * 60)
        
        max_qps = max([r['requests_per_second'] for r in results])
        avg_success_rate = sum([r['success_rate'] for r in results]) / len(results)
        max_concurrent = max([r['concurrent_users'] for r in results])
        
        print(f"🏆 最高 QPS: {max_qps:.1f} 请求/秒")
        print(f"📈 平均成功率: {avg_success_rate:.1f}%")
        print(f"👥 最大测试并发: {max_concurrent} 用户")
        
        # 性能等级
        if max_qps > 1000:
            level = "🏆 卓越"
        elif max_qps > 500:
            level = "🥇 优秀"
        elif max_qps > 200:
            level = "🥈 良好"
        else:
            level = "🥉 一般"
        
        print(f"🎯 性能等级: {level}")
        
        # 并发支持评估
        stable_results = [r for r in results if r['success_rate'] > 95]
        if stable_results:
            stable_qps = max([r['requests_per_second'] for r in stable_results])
            print(f"\n🚀 稳定并发支持能力")
            print("=" * 60)
            print(f"• 稳定 QPS: {stable_qps:.1f}")
            print(f"• 理论并发支持: {int(stable_qps * 60)} 用户/分钟")
            print(f"• 推荐生产并发: {int(stable_qps * 10)} 用户")

async def main():
    """主函数"""
    tester = HighPerformanceTester()
    await tester.run_high_performance_test()

if __name__ == "__main__":
    asyncio.run(main())
