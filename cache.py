#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
@File	:cache.py
@Time	:2025/05/28
<AUTHOR>
@Mail	:<EMAIL>
"""

# POI搜索结果缓存
_poi_cache = {}

async def cached_poi_search(poi_seacher, poi_input):
    """
    缓存POI搜索结果，避免重复调用API
    
    Args:
        poi_seacher: POI搜索工具
        poi_input: 搜索参数
        
    Returns:
        搜索结果，优先从缓存获取
    """
    # 将输入参数转换为缓存键
    cache_key = f"{poi_input['params'].to_location_detail}_{poi_input['params'].to_location_lat_lng}"
    
    # 检查缓存中是否存在结果
    if cache_key in _poi_cache:
        return _poi_cache[cache_key]
    
    # 如果缓存中不存在结果，则使用API进行搜索并缓存结果
    result = await poi_seacher.ainvoke(poi_input)
    _poi_cache[cache_key] = result
    return result
