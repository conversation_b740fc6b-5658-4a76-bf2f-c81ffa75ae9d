// 约会规划服务前端脚本 - 版本 2.0

console.log('DatingPlannerClient v2.0 loaded');

class DatingPlannerClient {
    constructor() {
        this.initializeEventListeners();
    }

    // 通用SSE消息处理函数
    handleSSEMessage(data, contentDiv, statusDiv) {
        // 处理错误消息
        if (data.error) {
            contentDiv.textContent += '❌ 错误: ' + data.error + '\n';
            statusDiv.textContent = '请求失败';
            statusDiv.className = 'status error';
            return false; // 表示遇到错误，应该停止处理
        }

        // 处理信息消息
        if (data.content) {
            contentDiv.textContent += data.content;
            contentDiv.scrollTop = contentDiv.scrollHeight;
        }

        // 处理成功消息
        if (data.message) {
            statusDiv.textContent = data.message;
            statusDiv.className = 'status completed';
        }

        return true; // 继续处理
    }

    initializeEventListeners() {
        document.getElementById('explainBtn').addEventListener('click', () => this.explainRequest());
        document.getElementById('thinkPlanBtn').addEventListener('click', () => this.thinkPlan());
        document.getElementById('searchLocationBtn').addEventListener('click', () => this.searchLocationInfo());
        document.getElementById('detailedPlanBtn').addEventListener('click', () => this.getDetailedPlan());
    }

    // 收集表单数据
    collectFormData() {
        const form = document.getElementById('datingForm');
        const formData = new FormData(form);

        const params = {
            user_input: formData.get('userInput'),
            request_type: formData.get('requestType'),
            dating_params: {
                to_location_province: formData.get('toLocationProvince'),
                to_location_city: formData.get('toLocationCity'),
                to_location_district: formData.get('toLocationDistrict'),
                to_location_name: formData.get('toLocationName'),

                user_lat_lng: "113.999706,22.588863",
                user_location_province: "广东省",
                user_location_city: "深圳市",
                user_location_district: formData.get('userLocationDistrict'),
                user_location_name: formData.get('userLocationName'),

                departure_lat_lng: "113.999706,22.588863",
                departure_province: "广东省",
                departure_city: "深圳市",
                departure_district: formData.get('userLocationDistrict'),
                departure_name: formData.get('userLocationName'),

                comp_lat_lng: "114.054007,22.533569",
                comp_location_province: "广东省",
                comp_location_city: "深圳市",
                comp_location_district: formData.get('compLocationDistrict'),
                comp_location_name: formData.get('compLocationName'),

                male_last_name: "张",
                female_last_name: "李",

                trans_tool_user: formData.get('transToolUser'),
                trans_tool_comp: formData.get('transToolComp'),

                date: formData.get('date'),
                time_start: formData.get('timeStart'),
                time_end: formData.get('timeEnd'),

                dating_times: 1,
                dating_type: formData.get('datingType'),
                more_thoughts: formData.get('moreThoughts')
            }
        };

        return params;
    }

    // 解释约会期望 - SSE
    async explainRequest() {
        const btn = document.getElementById('explainBtn');
        const contentDiv = document.querySelector('#explainResult .content');
        const statusDiv = document.querySelector('#explainResult .status');

        try {
            btn.disabled = true;
            btn.innerHTML = '<span class="loading"></span>处理中...';
            contentDiv.textContent = '';
            statusDiv.textContent = '正在连接...';
            statusDiv.className = 'status connecting';

            const params = this.collectFormData();

            const response = await fetch('/explain-request', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(params)
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            statusDiv.textContent = '已连接，正在接收数据...';
            statusDiv.className = 'status connected';

            const reader = response.body.getReader();
            const decoder = new TextDecoder();

            while (true) {
                const { done, value } = await reader.read();
                if (done) break;

                const chunk = decoder.decode(value);
                const lines = chunk.split('\n');

                for (const line of lines) {
                    if (line.startsWith('data: ')) {
                        try {
                            const jsonStr = line.slice(6).trim();
                            if (jsonStr) {
                                const data = JSON.parse(jsonStr);
                                if (!this.handleSSEMessage(data, contentDiv, statusDiv)) {
                                    return; // 遇到错误，停止处理
                                }
                            }
                        } catch (e) {
                            console.log('JSON parse error:', e, 'Line:', line);
                            // 忽略解析错误
                        }
                    }
                }
            }

            statusDiv.textContent = '约会期望解释完成';
            statusDiv.className = 'status completed';

        } catch (error) {
            contentDiv.textContent = '请求失败: ' + error.message;
            statusDiv.textContent = '连接错误';
            statusDiv.className = 'status error';
        } finally {
            btn.disabled = false;
            btn.textContent = '解释约会期望';
        }
    }

    // 思考规划 - SSE
    async thinkPlan() {
        const btn = document.getElementById('thinkPlanBtn');
        const contentDiv = document.querySelector('#thinkPlanResult .content');
        const statusDiv = document.querySelector('#thinkPlanResult .status');

        try {
            btn.disabled = true;
            btn.innerHTML = '<span class="loading"></span>思考中...';
            contentDiv.textContent = '';
            statusDiv.textContent = '正在连接...';
            statusDiv.className = 'status connecting';

            const params = this.collectFormData();

            // 使用 fetch 发送 POST 请求到 SSE 端点
            const response = await fetch('/think-plan', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(params)
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            statusDiv.textContent = '已连接，正在接收数据...';
            statusDiv.className = 'status connected';

            const reader = response.body.getReader();
            const decoder = new TextDecoder();

            while (true) {
                const { done, value } = await reader.read();
                if (done) break;

                const chunk = decoder.decode(value);
                const lines = chunk.split('\n');

                for (const line of lines) {
                    if (line.startsWith('data: ')) {
                        try {
                            const jsonStr = line.slice(6).trim();
                            if (jsonStr) {
                                const data = JSON.parse(jsonStr);
                                if (!this.handleSSEMessage(data, contentDiv, statusDiv)) {
                                    return; // 遇到错误，停止处理
                                }
                            }
                        } catch (e) {
                            console.log('JSON parse error:', e, 'Line:', line);
                            // 忽略解析错误
                        }
                    }
                }
            }

            statusDiv.textContent = '思考规划完成';
            statusDiv.className = 'status completed';

        } catch (error) {
            contentDiv.textContent = '请求失败: ' + error.message;
            statusDiv.textContent = '连接错误';
            statusDiv.className = 'status error';
        } finally {
            btn.disabled = false;
            btn.textContent = '思考规划';
        }
    }

    // 查询目的地信息 - SSE
    async searchLocationInfo() {
        const btn = document.getElementById('searchLocationBtn');
        const contentDiv = document.querySelector('#locationResult .content');
        const statusDiv = document.querySelector('#locationResult .status');

        try {
            btn.disabled = true;
            btn.innerHTML = '<span class="loading"></span>查询中...';
            contentDiv.textContent = '';
            statusDiv.textContent = '正在连接...';
            statusDiv.className = 'status connecting';

            const params = this.collectFormData();

            const response = await fetch('/search-location-info', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(params)
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            statusDiv.textContent = '已连接，正在接收数据...';
            statusDiv.className = 'status connected';

            const reader = response.body.getReader();
            const decoder = new TextDecoder();

            while (true) {
                const { done, value } = await reader.read();
                if (done) break;

                const chunk = decoder.decode(value);
                const lines = chunk.split('\n');

                for (const line of lines) {
                    if (line.startsWith('data: ')) {
                        console.log('Received data:', line);
                        try {
                            const jsonStr = line.slice(6).trim();
                            if (jsonStr) {
                                const data = JSON.parse(jsonStr);
                                if (!this.handleSSEMessage(data, contentDiv, statusDiv)) {
                                    return; // 遇到错误，停止处理
                                }
                            }
                        } catch (e) {
                            console.log('JSON parse error:', e, 'Line:', line);
                            // 忽略解析错误
                        }
                    }
                }
            }

            statusDiv.textContent = '目的地信息查询完成';
            statusDiv.className = 'status completed';

        } catch (error) {
            contentDiv.textContent = '请求失败: ' + error.message;
            statusDiv.textContent = '连接错误';
            statusDiv.className = 'status error';
        } finally {
            btn.disabled = false;
            btn.textContent = '查询目的地信息';
        }
    }

    // 获取详细计划 - SSE
    async getDetailedPlan() {
        const btn = document.getElementById('detailedPlanBtn');
        const contentDiv = document.querySelector('#detailedPlanResult .content');
        const statusDiv = document.querySelector('#detailedPlanResult .status');

        try {
            btn.disabled = true;
            btn.innerHTML = '<span class="loading"></span>规划中...';
            contentDiv.textContent = '';
            statusDiv.textContent = '正在连接...';
            statusDiv.className = 'status connecting';

            const params = this.collectFormData();

            const response = await fetch('/get-detailed-plan', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(params)
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            statusDiv.textContent = '已连接，正在接收数据...';
            statusDiv.className = 'status connected';

            const reader = response.body.getReader();
            const decoder = new TextDecoder();

            while (true) {
                const { done, value } = await reader.read();
                if (done) break;

                const chunk = decoder.decode(value);
                const lines = chunk.split('\n');

                for (const line of lines) {
                    if (line.startsWith('data: ')) {
                        try {
                            const jsonStr = line.slice(6).trim();
                            if (jsonStr) {
                                const data = JSON.parse(jsonStr);
                                if (!this.handleSSEMessage(data, contentDiv, statusDiv)) {
                                    return; // 遇到错误，停止处理
                                }
                            }
                        } catch (e) {
                            console.log('JSON parse error:', e, 'Line:', line);
                            // 忽略解析错误
                        }
                    }
                }
            }

            statusDiv.textContent = '详细规划完成';
            statusDiv.className = 'status completed';

        } catch (error) {
            contentDiv.textContent = '请求失败: ' + error.message;
            statusDiv.textContent = '连接错误';
            statusDiv.className = 'status error';
        } finally {
            btn.disabled = false;
            btn.textContent = '获取详细计划';
        }
    }
}

// 初始化应用
document.addEventListener('DOMContentLoaded', () => {
    new DatingPlannerClient();
});
