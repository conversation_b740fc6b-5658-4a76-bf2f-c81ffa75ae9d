#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
性能优化模块 - 约会规划服务
包含连接池、缓存、限流等优化功能
"""

import asyncio
import aiohttp
from typing import Optional, Dict, Any
from functools import wraps
import hashlib
import json
import time
from contextlib import asynccontextmanager

class PerformanceOptimizer:
    """性能优化器"""

    def __init__(self):
        self.http_session: Optional[aiohttp.ClientSession] = None
        self.memory_cache: Dict[str, Dict] = {}  # 内存缓存
        self.request_semaphore = asyncio.Semaphore(50)  # 总体并发限制
        self.openai_api_semaphore = asyncio.Semaphore(15)  # OpenAI API并发限制（增加到15）
        self.poi_search_semaphore = asyncio.Semaphore(5)  # POI搜索并发限制
        self.weather_api_semaphore = asyncio.Semaphore(10)  # 天气API并发限制
        self.traffic_api_semaphore = asyncio.Semaphore(10)  # 交通API并发限制
        self.rate_limiter = {}  # 简单的内存限流器

    async def initialize(self):
        """初始化连接池和缓存"""
        # HTTP 连接池
        connector = aiohttp.TCPConnector(
            limit=100,  # 总连接数
            limit_per_host=20,  # 每个主机的连接数
            ttl_dns_cache=300,  # DNS 缓存时间
            use_dns_cache=True,
            keepalive_timeout=30,
            enable_cleanup_closed=True
        )

        timeout = aiohttp.ClientTimeout(
            total=30,  # 总超时时间
            connect=5,  # 连接超时
            sock_read=10  # 读取超时
        )

        self.http_session = aiohttp.ClientSession(
            connector=connector,
            timeout=timeout
        )

        # 使用内存缓存（简化版）
        print("使用内存缓存系统")

    async def cleanup(self):
        """清理资源"""
        if self.http_session:
            await self.http_session.close()
        # 清理内存缓存
        self.memory_cache.clear()

    def rate_limit(self, max_requests: int = 10, window: int = 60):
        """限流装饰器"""
        def decorator(func):
            @wraps(func)
            async def wrapper(*args, **kwargs):
                # 简单的滑动窗口限流
                client_id = kwargs.get('client_id', 'default')
                now = time.time()

                if client_id not in self.rate_limiter:
                    self.rate_limiter[client_id] = []

                # 清理过期请求
                self.rate_limiter[client_id] = [
                    req_time for req_time in self.rate_limiter[client_id]
                    if now - req_time < window
                ]

                # 检查是否超过限制
                if len(self.rate_limiter[client_id]) >= max_requests:
                    raise Exception(f"请求频率过高，请稍后再试")

                self.rate_limiter[client_id].append(now)
                return await func(*args, **kwargs)
            return wrapper
        return decorator

    async def cached_request(self, cache_key: str, cache_ttl: int = 3600):
        """缓存装饰器"""
        def decorator(func):
            @wraps(func)
            async def wrapper(*args, **kwargs):
                # 生成缓存键
                key_data = f"{func.__name__}:{cache_key}:{str(args)}:{str(kwargs)}"
                cache_key_hash = hashlib.md5(key_data.encode()).hexdigest()

                # 尝试从缓存获取
                cached_result = await self._get_cache(cache_key_hash)
                if cached_result:
                    return cached_result

                # 执行函数并缓存结果
                result = await func(*args, **kwargs)
                await self._set_cache(cache_key_hash, result, cache_ttl)
                return result
            return wrapper
        return decorator

    async def _get_cache(self, key: str) -> Optional[Any]:
        """获取缓存"""
        try:
            cache_entry = self.memory_cache.get(key)
            if cache_entry:
                # 检查是否过期
                if time.time() < cache_entry['expires_at']:
                    return cache_entry['value']
                else:
                    # 过期了，删除缓存
                    del self.memory_cache[key]
            return None
        except Exception:
            return None

    async def _set_cache(self, key: str, value: Any, ttl: int):
        """设置缓存"""
        try:
            expires_at = time.time() + ttl
            self.memory_cache[key] = {
                'value': value,
                'expires_at': expires_at
            }
            # 定期清理过期缓存
            asyncio.create_task(self._cleanup_expired_cache())
        except Exception:
            pass

    async def _cleanup_expired_cache(self):
        """清理过期缓存"""
        try:
            current_time = time.time()
            expired_keys = [
                key for key, entry in self.memory_cache.items()
                if current_time >= entry['expires_at']
            ]
            for key in expired_keys:
                del self.memory_cache[key]
        except Exception:
            pass

    @asynccontextmanager
    async def concurrent_limit(self):
        """并发限制上下文管理器"""
        async with self.request_semaphore:
            yield

    async def batch_request(self, requests: list, max_concurrent: int = 10):
        """批量请求处理"""
        semaphore = asyncio.Semaphore(max_concurrent)

        async def limited_request(request_func):
            async with semaphore:
                return await request_func()

        tasks = [limited_request(req) for req in requests]
        return await asyncio.gather(*tasks, return_exceptions=True)

# 全局优化器实例
optimizer = PerformanceOptimizer()

# 优化后的 HTTP 客户端
class OptimizedHTTPClient:
    """优化的 HTTP 客户端"""

    @staticmethod
    async def get(url: str, params: dict = None, **kwargs) -> dict:
        """优化的 GET 请求"""
        async with optimizer.concurrent_limit():
            if not optimizer.http_session:
                await optimizer.initialize()

            try:
                async with optimizer.http_session.get(url, params=params, **kwargs) as response:
                    response.raise_for_status()
                    return await response.json()
            except asyncio.TimeoutError:
                raise Exception("请求超时")
            except aiohttp.ClientError as e:
                raise Exception(f"网络请求失败: {str(e)}")

    @staticmethod
    async def post(url: str, data: dict = None, json_data: dict = None, **kwargs) -> dict:
        """优化的 POST 请求"""
        async with optimizer.concurrent_limit():
            if not optimizer.http_session:
                await optimizer.initialize()

            try:
                async with optimizer.http_session.post(url, data=data, json=json_data, **kwargs) as response:
                    response.raise_for_status()
                    return await response.json()
            except asyncio.TimeoutError:
                raise Exception("请求超时")
            except aiohttp.ClientError as e:
                raise Exception(f"网络请求失败: {str(e)}")

# 性能监控装饰器
def monitor_performance(func):
    """性能监控装饰器"""
    @wraps(func)
    async def wrapper(*args, **kwargs):
        start_time = time.time()
        try:
            result = await func(*args, **kwargs)
            duration = time.time() - start_time
            print(f"[PERF] {func.__name__} 执行时间: {duration:.2f}s")
            return result
        except Exception as e:
            duration = time.time() - start_time
            print(f"[PERF] {func.__name__} 执行失败: {duration:.2f}s, 错误: {str(e)}")
            raise
    return wrapper

# 应用启动和关闭事件
async def startup_event():
    """应用启动事件"""
    await optimizer.initialize()
    print("性能优化器已初始化")

async def shutdown_event():
    """应用关闭事件"""
    await optimizer.cleanup()
    print("性能优化器已清理")
