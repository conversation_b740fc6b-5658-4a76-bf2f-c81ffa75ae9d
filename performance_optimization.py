#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
性能优化模块 - 约会规划服务
包含连接池、缓存、限流等优化功能
"""

import asyncio
import aiohttp
import aioredis
from typing import Optional, Dict, Any
from functools import wraps
from datetime import datetime, timedelta
import hashlib
import json
import time
from contextlib import asynccontextmanager

class PerformanceOptimizer:
    """性能优化器"""
    
    def __init__(self):
        self.http_session: Optional[aiohttp.ClientSession] = None
        self.redis_client: Optional[aioredis.Redis] = None
        self.request_semaphore = asyncio.Semaphore(50)  # 限制并发数
        self.rate_limiter = {}  # 简单的内存限流器
        
    async def initialize(self):
        """初始化连接池和缓存"""
        # HTTP 连接池
        connector = aiohttp.TCPConnector(
            limit=100,  # 总连接数
            limit_per_host=20,  # 每个主机的连接数
            ttl_dns_cache=300,  # DNS 缓存时间
            use_dns_cache=True,
            keepalive_timeout=30,
            enable_cleanup_closed=True
        )
        
        timeout = aiohttp.ClientTimeout(
            total=30,  # 总超时时间
            connect=5,  # 连接超时
            sock_read=10  # 读取超时
        )
        
        self.http_session = aiohttp.ClientSession(
            connector=connector,
            timeout=timeout
        )
        
        # Redis 缓存（可选）
        try:
            self.redis_client = await aioredis.from_url(
                "redis://localhost:6379",
                encoding="utf-8",
                decode_responses=True,
                max_connections=20
            )
        except Exception as e:
            print(f"Redis 连接失败，使用内存缓存: {e}")
            self.redis_client = None
            
    async def cleanup(self):
        """清理资源"""
        if self.http_session:
            await self.http_session.close()
        if self.redis_client:
            await self.redis_client.close()

    def rate_limit(self, max_requests: int = 10, window: int = 60):
        """限流装饰器"""
        def decorator(func):
            @wraps(func)
            async def wrapper(*args, **kwargs):
                # 简单的滑动窗口限流
                client_id = kwargs.get('client_id', 'default')
                now = time.time()
                
                if client_id not in self.rate_limiter:
                    self.rate_limiter[client_id] = []
                
                # 清理过期请求
                self.rate_limiter[client_id] = [
                    req_time for req_time in self.rate_limiter[client_id]
                    if now - req_time < window
                ]
                
                # 检查是否超过限制
                if len(self.rate_limiter[client_id]) >= max_requests:
                    raise Exception(f"请求频率过高，请稍后再试")
                
                self.rate_limiter[client_id].append(now)
                return await func(*args, **kwargs)
            return wrapper
        return decorator

    async def cached_request(self, cache_key: str, cache_ttl: int = 3600):
        """缓存装饰器"""
        def decorator(func):
            @wraps(func)
            async def wrapper(*args, **kwargs):
                # 生成缓存键
                key_data = f"{func.__name__}:{cache_key}:{str(args)}:{str(kwargs)}"
                cache_key_hash = hashlib.md5(key_data.encode()).hexdigest()
                
                # 尝试从缓存获取
                cached_result = await self._get_cache(cache_key_hash)
                if cached_result:
                    return cached_result
                
                # 执行函数并缓存结果
                result = await func(*args, **kwargs)
                await self._set_cache(cache_key_hash, result, cache_ttl)
                return result
            return wrapper
        return decorator

    async def _get_cache(self, key: str) -> Optional[Any]:
        """获取缓存"""
        try:
            if self.redis_client:
                data = await self.redis_client.get(key)
                return json.loads(data) if data else None
            else:
                # 内存缓存（简单实现）
                return getattr(self, f'_cache_{key}', None)
        except Exception:
            return None

    async def _set_cache(self, key: str, value: Any, ttl: int):
        """设置缓存"""
        try:
            if self.redis_client:
                await self.redis_client.setex(key, ttl, json.dumps(value))
            else:
                # 内存缓存（简单实现）
                setattr(self, f'_cache_{key}', value)
                # 设置过期时间（简化版）
                asyncio.create_task(self._expire_cache(key, ttl))
        except Exception:
            pass

    async def _expire_cache(self, key: str, ttl: int):
        """缓存过期处理"""
        await asyncio.sleep(ttl)
        try:
            delattr(self, f'_cache_{key}')
        except AttributeError:
            pass

    @asynccontextmanager
    async def concurrent_limit(self):
        """并发限制上下文管理器"""
        async with self.request_semaphore:
            yield

    async def batch_request(self, requests: list, max_concurrent: int = 10):
        """批量请求处理"""
        semaphore = asyncio.Semaphore(max_concurrent)
        
        async def limited_request(request_func):
            async with semaphore:
                return await request_func()
        
        tasks = [limited_request(req) for req in requests]
        return await asyncio.gather(*tasks, return_exceptions=True)

# 全局优化器实例
optimizer = PerformanceOptimizer()

# 优化后的 HTTP 客户端
class OptimizedHTTPClient:
    """优化的 HTTP 客户端"""
    
    @staticmethod
    async def get(url: str, params: dict = None, **kwargs) -> dict:
        """优化的 GET 请求"""
        async with optimizer.concurrent_limit():
            if not optimizer.http_session:
                await optimizer.initialize()
            
            try:
                async with optimizer.http_session.get(url, params=params, **kwargs) as response:
                    response.raise_for_status()
                    return await response.json()
            except asyncio.TimeoutError:
                raise Exception("请求超时")
            except aiohttp.ClientError as e:
                raise Exception(f"网络请求失败: {str(e)}")

    @staticmethod
    async def post(url: str, data: dict = None, json_data: dict = None, **kwargs) -> dict:
        """优化的 POST 请求"""
        async with optimizer.concurrent_limit():
            if not optimizer.http_session:
                await optimizer.initialize()
            
            try:
                async with optimizer.http_session.post(url, data=data, json=json_data, **kwargs) as response:
                    response.raise_for_status()
                    return await response.json()
            except asyncio.TimeoutError:
                raise Exception("请求超时")
            except aiohttp.ClientError as e:
                raise Exception(f"网络请求失败: {str(e)}")

# 性能监控装饰器
def monitor_performance(func):
    """性能监控装饰器"""
    @wraps(func)
    async def wrapper(*args, **kwargs):
        start_time = time.time()
        try:
            result = await func(*args, **kwargs)
            duration = time.time() - start_time
            print(f"[PERF] {func.__name__} 执行时间: {duration:.2f}s")
            return result
        except Exception as e:
            duration = time.time() - start_time
            print(f"[PERF] {func.__name__} 执行失败: {duration:.2f}s, 错误: {str(e)}")
            raise
    return wrapper

# 应用启动和关闭事件
async def startup_event():
    """应用启动事件"""
    await optimizer.initialize()
    print("性能优化器已初始化")

async def shutdown_event():
    """应用关闭事件"""
    await optimizer.cleanup()
    print("性能优化器已清理")
