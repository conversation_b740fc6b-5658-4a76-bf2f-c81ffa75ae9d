#!/usr/bin/env python
# -*- coding: utf-8 -*-
'''
@File	:main.py
@Time	:2025/05/15 15:23:12
<AUTHOR>
@Mail	:<EMAIL>
'''

import asyncio

from pydantic.v1.utils import to_lower_camel
from prompt import THINK_PLAN_PROMPT
from llm import async_client
from tools import poi_seacher, PoiInput
from location_helper import LocationHelper
from dating_planner import DatingPlanner


params = {
    'user_input': '这周六下午我想和女朋友在COCO Park约会',  # 用户输入
    'request_type': 'dating',  # 请求类型 'dating':约会规划，'chat':对话聊天
    'dating_params': {
        'to_location_province': '广东省',  # 目的地省份
        'to_location_city': '深圳市',  # 目的地城市
        'to_location_district': '福田区',  # 目的地区县
        'to_location_name': 'COCO Park',  # 目的地名称

        'user_lat_lng': '113.999706,22.588863',  # 用户位置经纬度
        'user_location_province': '广东省',  # 用户位置省份
        'user_location_city': '深圳市',  # 用户位置城市
        'user_location_district': '南山区',  # 用户位置区县
        'user_location_name': '塘朗城广场',  # 用户位置名称

        'departure_lat_lng': '113.999706,22.588863',  # 出发地经纬度
        'departure_province': '广东省',  # 出发地省份
        'departure_city': '深圳市',  # 出发地城市
        'departure_district': '南山区',  # 出发地区县
        'departure_name': '塘朗城广场',  # 出发地名称

        'comp_lat_lng': '114.054007,22.533569',  # 伴侣位置经纬度
        'comp_location_province': '广东省',  # 伴侣位置省份
        'comp_location_city': '深圳市',  # 伴侣位置城市
        'comp_location_district': '宝安区',  # 伴侣位置区县
        'comp_location_name': '尚都花园',  # 伴侣位置名称

        'male_last_name': '张',  # 男方姓氏
        'female_last_name': '李',  # 女方姓氏

        'trans_tool_user': 'bus',  # 用户交通方式 bus-公交 car-自驾
        'trans_tool_comp': 'bus',  # 伴侣交通方式 bus-公交 car-自驾

        'date': '2025-05-24',  # 约会日期
        'time_start': '14:00',  # 约会开始时间
        'time_end': '17:00',  # 约会结束时间，

        'dating_times': 1,  # 约会次数 1-1次 2-2次 3-3多次 4-确认关系

        'dating_type': '浪漫型',  # 约会偏好 浪漫型/趣味型/文艺型

        'more_thoughts': '我希望约会时可以吃到冰淇淋'  # 更多想法
    }
}


location_helper = LocationHelper()
dating_planner = DatingPlanner()


# 第一步 阐释用户目的
def explain_request(params: dict):
    """
    解释用户约会期望
    Args:
        params (dict): 请求参数
    Returns:
        str: 解释后的字符串
    """

    trans_tool_dict = {
        'bus': '公交',
        'car': '自驾'
    }
    user_departure = params['dating_params']['departure_district']
    user_trans_tool = trans_tool_dict[params['dating_params']
                                      ['trans_tool_user']]
    comp_departure = params['dating_params']['comp_location_district']
    comp_trans_tool = trans_tool_dict[params['dating_params']
                                      ['trans_tool_comp']]
    dating_date = params['dating_params']['date']
    dating_time_start = params['dating_params']['time_start']
    dating_time_end = params['dating_params']['time_end']
    dating_area = params['dating_params']['to_location_district'] + \
        params['dating_params']['to_location_name']
    dating_type = params['dating_params']['dating_type']

    explain = f'用户出发地:{user_departure}、{user_trans_tool};伴侣出发地:{comp_departure}、{comp_trans_tool};约会时间:{dating_date} {dating_time_start}-{dating_time_end};约会区域范围为{dating_area};约会格调为{dating_type}'

    return explain


async def think_plan(params: dict):
    """
    用户需求理解和任务拆分
    """
    explain = explain_request(params)
    know_info = {
        'know_info': explain
    }
    full_prompt = THINK_PLAN_PROMPT.format(**know_info)
    stream = await async_client.chat.completions.create(model="qwen-plus-2025-01-12", messages=[{"role": "system", "content": full_prompt}], stream=True)
    async for chunk in stream:
        if chunk.choices[0].delta.content is not None:
            content = chunk.choices[0].delta.content
            yield content


async def search_purpose_location_info(params: dict):
    """
    查询目的地信息(热门景点、美食、购物、娱乐照片及数量)
    """

    to_location_detail = {
        # 广东省深圳市COCO Park
        'to_location_detail': params['dating_params']['to_location_province'] + params['dating_params']['to_location_city'] + params['dating_params']['to_location_name'],
        # 广东省深圳市|COCO Park
        'to_location_detail_mark': params['dating_params']['to_location_province'] + params['dating_params']['to_location_city'] + '|' + params['dating_params']['to_location_name'],
        'to_location_date': params['dating_params']['date']  # 2025-05-25
    }

    to_location_lat_lng = await location_helper.to_location2lat_lng(to_location_detail['to_location_detail'])
    to_location_lat_lng = to_location_lat_lng['location']
    to_location_adcode_info = await location_helper.lat_lng2city(to_location_lat_lng)
    to_location_adcode = to_location_adcode_info['adcode']

    to_location_detail_info = {
        'to_location_province': params['dating_params']['to_location_province'],
        'to_location_city': params['dating_params']['to_location_city'],
        'to_location_detail': to_location_detail,
        'to_location_adcode': to_location_adcode,
        'to_location_lat_lng': to_location_lat_lng,
        'user_location_province': params['dating_params']['user_location_province'],
        'user_location_city': params['dating_params']['user_location_city'],
        'user_location_district': params['dating_params']['user_location_district'],
        'to_location_name': params['dating_params']['to_location_name'],
        'to_location_date': params['dating_params']['date'],
        'to_location_time_start': params['dating_params']['time_start'],
        'to_location_time_end': params['dating_params']['time_end']
    }

    poi_input = {"params": PoiInput(location_helper=location_helper,
                                    to_location_detail=to_location_detail['to_location_detail'], to_location_lat_lng=to_location_lat_lng)}
    poi_result = await poi_seacher.ainvoke(poi_input)

    to_location_food = [{'name': i['name'], 'photos': i['photos']}
                        for i in poi_result['to_location_food']]
    to_location_shopping = [{'name': i['name'], 'photos': i['photos']}
                            for i in poi_result['to_location_shopping']]
    to_location_entertainment = [{'name': i['name'], 'photos': i['photos']}
                                 for i in poi_result['to_location_entertainment']]
    to_location_sightseeing = [{'name': i['name'], 'photos': i['photos']}
                               for i in poi_result['to_location_sightseeing']]
    count = len(to_location_food) + len(to_location_shopping) + \
        len(to_location_entertainment) + len(to_location_sightseeing)

    return {
        'to_location_food': to_location_food,
        'to_location_shopping': to_location_shopping,
        'to_location_entertainment': to_location_entertainment,
        'to_location_sightseeing': to_location_sightseeing,
        'count': count
    }


async def get_detailed_plan(params: dict):
    async for content in dating_planner.dating_planner(params):
        yield content


# async def main(user_input: str, ):
#     pass


# if __name__ == "__main__":
#     asyncio.run(main())
