<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>约会规划服务</title>
    <link rel="stylesheet" href="/static/style.css">
</head>
<body>
    <div class="container">
        <h1>约会规划服务</h1>

        <div class="form-section">
            <h2>约会参数设置</h2>
            <form id="datingForm">
                <div class="form-group">
                    <label for="userInput">用户输入:</label>
                    <input type="text" id="userInput" name="userInput" value="怎样避免约会的时候双方陷入沉默?" required>
                </div>

                <div class="form-group">
                    <label for="requestType">请求类型:</label>
                    <select id="requestType" name="requestType">
                        <option value="dating">约会规划</option>
                        <option value="chat">对话聊天</option>
                    </select>
                </div>

                <h3>约会详细参数</h3>

                <div class="form-row">
                    <div class="form-group">
                        <label for="toLocationProvince">目的地省份:</label>
                        <input type="text" id="toLocationProvince" name="toLocationProvince" value="广东省">
                    </div>
                    <div class="form-group">
                        <label for="toLocationCity">目的地城市:</label>
                        <input type="text" id="toLocationCity" name="toLocationCity" value="深圳市">
                    </div>
                    <div class="form-group">
                        <label for="toLocationDistrict">目的地区县:</label>
                        <input type="text" id="toLocationDistrict" name="toLocationDistrict" value="宝安区">
                    </div>
                    <div class="form-group">
                        <label for="toLocationName">目的地名称:</label>
                        <input type="text" id="toLocationName" name="toLocationName" value="西乡">
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="userLocationDistrict">用户出发地区县:</label>
                        <input type="text" id="userLocationDistrict" name="userLocationDistrict" value="南山区">
                    </div>
                    <div class="form-group">
                        <label for="userLocationName">用户出发地名称:</label>
                        <input type="text" id="userLocationName" name="userLocationName" value="塘朗城广场">
                    </div>
                    <div class="form-group">
                        <label for="compLocationDistrict">伴侣位置区县:</label>
                        <input type="text" id="compLocationDistrict" name="compLocationDistrict" value="龙岗区">
                    </div>
                    <div class="form-group">
                        <label for="compLocationName">伴侣位置名称:</label>
                        <input type="text" id="compLocationName" name="compLocationName" value="怡乐花园">
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="date">约会日期:</label>
                        <input type="date" id="date" name="date" value="2025-05-30">
                    </div>
                    <div class="form-group">
                        <label for="timeStart">开始时间:</label>
                        <input type="time" id="timeStart" name="timeStart" value="09:00">
                    </div>
                    <div class="form-group">
                        <label for="timeEnd">结束时间:</label>
                        <input type="time" id="timeEnd" name="timeEnd" value="20:00">
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="transToolUser">用户交通方式:</label>
                        <select id="transToolUser" name="transToolUser">
                            <option value="bus">公交</option>
                            <option value="car">自驾</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="transToolComp">伴侣交通方式:</label>
                        <select id="transToolComp" name="transToolComp">
                            <option value="bus">公交</option>
                            <option value="car">自驾</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="datingType">约会偏好:</label>
                        <select id="datingType" name="datingType">
                            <option value="浪漫型">浪漫型</option>
                            <option value="趣味型">趣味型</option>
                            <option value="文艺型">文艺型</option>
                        </select>
                    </div>
                </div>

                <div class="form-group">
                    <label for="moreThoughts">更多想法:</label>
                    <textarea id="moreThoughts" name="moreThoughts" rows="3">我希望约会时可以吃到火锅</textarea>
                </div>
            </form>
        </div>

        <div class="buttons-section">
            <button id="explainBtn" class="btn btn-primary">解释约会期望</button>
            <button id="thinkPlanBtn" class="btn btn-secondary">思考规划</button>
            <button id="searchLocationBtn" class="btn btn-info">查询目的地信息</button>
            <button id="detailedPlanBtn" class="btn btn-success">获取详细计划</button>
        </div>

        <div class="results-section">
            <div id="explainResult" class="result-box">
                <h3>约会期望解释 (实时流式)</h3>
                <div class="content"></div>
                <div class="status"></div>
            </div>

            <div id="thinkPlanResult" class="result-box">
                <h3>思考规划过程 (实时流式)</h3>
                <div class="content"></div>
                <div class="status"></div>
            </div>

            <div id="locationResult" class="result-box">
                <h3>目的地信息 (实时流式)</h3>
                <div class="content"></div>
                <div class="status"></div>
            </div>

            <div id="detailedPlanResult" class="result-box">
                <h3>详细计划 (实时流式)</h3>
                <div class="content"></div>
                <div class="status"></div>
            </div>
        </div>
    </div>

    <script src="/static/script.js"></script>
</body>
</html>
